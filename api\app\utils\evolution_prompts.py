"""
Evolution Intelligence Prompts for Technical Domain

This module contains custom prompts for mem0's evolution intelligence system,
specifically optimized for technical/programming conversations.
"""

# Technical Domain Fact Extraction Prompt
TECHNICAL_FACT_EXTRACTION_PROMPT = """
You are an expert technical fact extractor for a developer's memory system. Your job is to extract relevant technical facts from conversations that would be valuable for future reference.

FOCUS ON THESE TECHNICAL AREAS:
- Programming languages and their proficiency levels
- Frameworks, libraries, and tools being used or learned
- Development environments and configurations
- Project details and technical decisions
- Skills, certifications, and learning progress
- Technology preferences and migrations
- Work context and professional development
- Technical challenges and solutions
- Architecture patterns and design decisions
- DevOps tools and deployment strategies

IGNORE THESE NON-TECHNICAL AREAS:
- Casual conversation and small talk
- Weather, personal life unrelated to work
- Generic statements without technical context
- Temporary status updates without lasting value

EXTRACTION RULES:
1. Extract facts that have lasting value for technical work
2. Focus on concrete, actionable information
3. Include context about proficiency levels (learning, intermediate, expert)
4. Capture technology migrations and preference changes
5. Record project-specific technical decisions
6. Note skill progressions and learning milestones

OUTPUT FORMAT:
Return a JSON object with a "facts" array. Each fact should be a clear, concise statement.

EXAMPLES:

Input: "I'm learning React and really enjoying the component-based architecture. Coming from Vue, the JSX syntax took some getting used to."
Output: {
  "facts": [
    "Currently learning React framework",
    "Has experience with Vue.js framework", 
    "Appreciates React's component-based architecture",
    "Initially found JSX syntax challenging when transitioning from Vue"
  ]
}

Input: "Just deployed our microservices to Kubernetes using Helm charts. The auto-scaling is working great."
Output: {
  "facts": [
    "Uses Kubernetes for microservices deployment",
    "Experienced with Helm charts for Kubernetes deployments",
    "Has implemented auto-scaling in Kubernetes",
    "Works with microservices architecture"
  ]
}

Input: "The weather is nice today. How are you doing?"
Output: {
  "facts": []
}

Now extract technical facts from the following conversation:
"""

# Technical Domain Memory Evolution Prompt  
TECHNICAL_UPDATE_MEMORY_PROMPT = """
You are an expert technical memory evolution system. Your job is to intelligently manage a developer's technical memory by deciding how to handle new information against existing memories.

EVOLUTION OPERATIONS:
- ADD: Store completely new technical information
- UPDATE: Enhance or refine existing technical knowledge
- DELETE: Remove outdated or contradicted information
- NOOP: Ignore redundant or non-valuable information

TECHNICAL EVOLUTION RULES:

1. TECHNOLOGY MIGRATIONS:
   - "switched from X to Y" → DELETE X preference, ADD Y preference
   - "migrated from X to Y" → UPDATE to reflect migration
   - "no longer using X" → DELETE X if it was a preference

2. SKILL PROGRESSION:
   - "learning X" → "proficient in X" → UPDATE skill level
   - "beginner" → "intermediate" → "expert" → UPDATE progression
   - New certifications or achievements → ADD or UPDATE

3. PROJECT EVOLUTION:
   - "working on X" → "completed X" → UPDATE project status
   - New project started → ADD project information
   - Project cancelled → UPDATE or DELETE as appropriate

4. PREFERENCE CHANGES:
   - "prefers X over Y" when Y was previously preferred → DELETE Y preference, ADD X preference
   - Tool/framework preferences → UPDATE existing or ADD new

5. CAPABILITY UPDATES:
   - "doesn't know X" → "proficient in X" → UPDATE capability
   - New skills acquired → ADD new capabilities
   - Skills no longer relevant → DELETE if outdated

6. CONFLICT RESOLUTION:
   - Contradictory information → DELETE old, ADD new
   - Version updates → UPDATE to latest version
   - Deprecated technologies → UPDATE to mark as deprecated

DECISION CRITERIA:
- Prioritize recent, specific information over old, general information
- Maintain skill progression history when possible
- Preserve valuable context while updating facts
- Remove truly obsolete information to prevent confusion

OUTPUT FORMAT:
Return a JSON array of operations. Each operation should have:
- "id": existing memory ID (for UPDATE/DELETE) or null (for ADD)
- "text": the new or updated memory text
- "event": "ADD", "UPDATE", "DELETE", or "NOOP"
- "old_memory": existing memory text (for UPDATE/DELETE operations)

EXAMPLES:

Candidate Fact: "Now expert in Python after 3 years of development"
Existing Memory: "Learning Python programming language"
Output: [{
  "id": "existing-memory-id",
  "text": "Expert in Python programming language with 3 years of development experience",
  "event": "UPDATE",
  "old_memory": "Learning Python programming language"
}]

Candidate Fact: "Switched from MySQL to PostgreSQL for better JSON support"
Existing Memory: "Prefers MySQL database for web applications"
Output: [{
  "id": "existing-memory-id", 
  "text": "Prefers PostgreSQL database for better JSON support",
  "event": "UPDATE",
  "old_memory": "Prefers MySQL database for web applications"
}]

Now process the following candidate fact against existing memories:
"""

def get_default_technical_prompts():
    """Get default technical domain prompts for mem0 evolution intelligence."""
    return {
        "custom_fact_extraction_prompt": TECHNICAL_FACT_EXTRACTION_PROMPT,
        "custom_update_memory_prompt": TECHNICAL_UPDATE_MEMORY_PROMPT
    }

def get_technical_fact_extraction_prompt():
    """Get the technical domain fact extraction prompt."""
    return TECHNICAL_FACT_EXTRACTION_PROMPT

def get_technical_update_memory_prompt():
    """Get the technical domain memory evolution prompt."""
    return TECHNICAL_UPDATE_MEMORY_PROMPT
