# PRD: Memory Master v2 - Self-Evolution Intelligence Enhancement

## Document Information

- **Document Type**: Product Requirements Document (PRD)
- **Feature Name**: Self-Evolution Intelligence Enhancement
- **Version**: 1.0
- **Target Release**: Memory Master v2.1
- **Author**: AI Assistant
- **Date**: June 29, 2025

---

## Executive Summary

This PRD defines the requirements for enhancing Memory Master v2's existing MCP server with mem0's built-in self-evolution intelligence. Instead of building custom evolution logic from scratch, this enhancement leverages mem0's proven two-phase pipeline (fact extraction + memory evolution) through custom prompt configuration. The enhancement transforms the current basic memory storage into an intelligent, adaptive system that automatically handles ADD/UPDATE/DELETE/NOOP operations with conflict resolution.

---

## Current State Analysis

### Existing System Architecture

#### MCP Server Layer (`api/app/mcp_server.py`)

- **Current Tools**: 5 MCP tools
    - `add_memories(text: str)` - Basic memory addition with chunking
    - `search_memory(query: str)` - Memory search functionality
    - `list_memories()` - Retrieve all memories
    - `get_system_health()` - System health diagnostics
    - `get_operation_metrics()` - Performance metrics
- **Request Handling**: Global request counter, context variables (user_id, client_name)
- **Error Handling**: Structured error responses with MCPErrorType classification
- **Validation**: Request validation with AddMemoryRequest models

#### Memory Service Layer (`api/app/memory_service.py`)

- **Core Operations**: Memory addition, search, listing with business logic separation
- **Chunking Logic**: Smart text chunking with sentence boundary preservation
- **Transaction Support**: Atomic operations for large text processing
- **Client Management**: Safe memory client retrieval with retry logic
- **Configuration Integration**: Dynamic max text length from database config

#### Memory Client Infrastructure (`api/app/utils/memory.py`)

- **MemoryClientSingleton**: Thread-safe singleton with comprehensive monitoring
- **Health Monitoring**: Automatic connectivity checking, degraded mode support
- **Configuration Management**: Database-driven config with environment variable parsing
- **Recovery Mechanisms**: Automatic recovery attempts with exponential backoff
- **Degradation Support**: Graceful fallback when vector store unavailable

#### Database Schema (`api/app/models.py`)

- **Core Tables**: users, apps, memories, categories, memory_categories
- **Audit Tables**: memory_status_history, memory_access_logs
- **Control Tables**: access_controls, archive_policies, configs
- **Relationships**: Proper foreign keys and indexes for performance
- **Schema**: All tables use `memory_master` schema

#### Testing Infrastructure

- **Comprehensive Test Suite**: 15+ test files covering all aspects
- **Test Categories**: Integration, performance, concurrent operations, health validation
- **Test Utilities**: Helper functions and fixtures in `tests/utils/test_helpers.py`

### Current Memory Processing Flow

1. **MCP Request**: `add_memories(text)` receives user input
2. **Context Validation**: Verify user_id and client_name from context variables
3. **Text Validation**: Check length limits, determine if chunking needed
4. **Memory Client**: Get thread-safe memory client singleton
5. **mem0 Processing**: Call `memory_client.add(text, user_id, metadata)`
6. **Database Update**: Store memory records with relationships
7. **Response**: Return success/error with processed memory details

### Current Limitations

- **Basic Storage**: mem0 uses default prompts, no domain specialization
- **No Evolution Tracking**: No visibility into ADD/UPDATE/DELETE/NOOP operations
- **No Conflict Resolution Visibility**: Can't see when contradictions are resolved
- **No Learning Analytics**: No metrics on memory evolution efficiency
- **Generic Fact Extraction**: Not optimized for technical/programming conversations

---

## Feature Overview: Self-Evolution Intelligence Enhancement

### Enhancement Strategy

**Leverage mem0's Built-in Intelligence** rather than building custom logic:

- **Phase 1 Enhancement**: Configure `custom_fact_extraction_prompt` for technical domain
- **Phase 2 Enhancement**: Configure `custom_update_memory_prompt` for programming contexts
- **Analytics Layer**: Track and visualize evolution operations
- **MCP Integration**: Expose evolution metrics through new MCP tools

### mem0's Two-Phase Evolution Pipeline

#### Phase 1: Enhanced Fact Extraction

- **Current**: mem0 uses default generic fact extraction prompts
- **Enhancement**: Configure domain-specific prompts for technical conversations
- **Target Content**: Programming languages, frameworks, tools, skills, projects, preferences
- **Output Format**: JSON with extracted facts array
- **Benefits**: Higher relevance for technical domain conversations

#### Phase 2: Enhanced Memory Evolution

- **Current**: mem0 uses default update logic with basic conflict resolution
- **Enhancement**: Configure technical domain rules for evolution decisions
- **Operations**: ADD (new info), UPDATE (enhance existing), DELETE (contradictions), NOOP (redundant)
- **Conflict Resolution**: Automatic handling of technology migrations, skill progressions, preference changes
- **Benefits**: Intelligent memory management without custom development

---

## Detailed Requirements

### 1. Enhanced Memory Configuration System

#### 1.1 Configuration Schema Extension

**Requirement**: Extend existing database configuration to support custom mem0 prompts

**Database Changes Required**:

- **Table**: `configs` (existing table, JSON column extension)
- **Schema Path**: `config.value.mem0.custom_fact_extraction_prompt`
- **Schema Path**: `config.value.mem0.custom_update_memory_prompt`
- **Schema Path**: `config.value.mem0.version` (ensure v1.1 for custom prompt support)

**Configuration Structure**:

```json
{
  "mem0": {
    "version": "v1.1",
    "custom_fact_extraction_prompt": "Technical domain extraction prompt",
    "custom_update_memory_prompt": "Technical domain evolution prompt", 
    "llm": { /* existing LLM config */ },
    "embedder": { /* existing embedder config */ },
    "vector_store": { /* existing vector store config */ }
  }
}
```

#### 1.2 Memory Client Configuration Enhancement

**File**: `api/app/utils/memory.py` **Function**: `get_memory_client()` **Requirement**: Integrate custom prompts into mem0 configuration

**Implementation Requirements**:

- Read custom prompts from database configuration
- Include prompts in mem0 config dictionary passed to `Memory.from_config()`
- Maintain backward compatibility with existing configurations
- Ensure prompts are applied to MemoryClientSingleton instances
- Log prompt configuration for debugging

#### 1.3 Technical Domain Prompts

**Requirement**: Create specialized prompts for technical/programming conversations

**Fact Extraction Prompt Requirements**:

- Focus on: Programming languages, frameworks, tools, skills, projects, work context
- Ignore: Casual conversation, weather, unrelated personal details
- Format: JSON output with facts array
- Examples: Few-shot examples for technical conversations
- Validation: Empty facts array for non-technical content

**Update Memory Prompt Requirements**:

- Handle technology migrations: "switched from X to Y" → DELETE X, ADD Y
- Handle skill progression: "learning X" → "expert in X" → UPDATE
- Handle project evolution: "working on X" → "completed X" → UPDATE
- Handle preference changes: "likes X" → "prefers Y" → DELETE X, ADD Y
- Handle capability updates: "doesn't know X" → "proficient in X" → UPDATE
- Output: Structured JSON with id, text, event, old_memory fields

### 2. Evolution Analytics Database Schema

#### 2.1 Evolution Operations Tracking Table

**Requirement**: Create new table to track all evolution operations

**Table**: `evolution_operations` **Schema**: `memory_master` **Purpose**: Track every ADD/UPDATE/DELETE/NOOP operation with context

**Columns Required**:

- `id`: UUID primary key
- `user_id`: UUID foreign key to users table
- `app_id`: UUID foreign key to apps table
- `memory_id`: UUID foreign key to memories table (nullable for failed operations)
- `operation_type`: ENUM('ADD', 'UPDATE', 'DELETE', 'NOOP')
- `candidate_fact`: TEXT - original fact extracted from conversation
- `existing_memory_content`: TEXT - content of existing memory (for UPDATE/DELETE)
- `similarity_score`: FLOAT - semantic similarity score (if available)
- `confidence_score`: FLOAT - LLM confidence in decision
- `reasoning`: TEXT - LLM reasoning for the decision
- `created_at`: TIMESTAMP WITH TIME ZONE
- `metadata`: JSONB - additional context (request_id, chunk_info, etc.)

**Indexes Required**:

- `idx_evolution_user_date` ON (user_id, created_at)
- `idx_evolution_app_operation` ON (app_id, operation_type)
- `idx_evolution_memory_id` ON (memory_id)
- `idx_evolution_operation_type` ON (operation_type)

#### 2.2 Evolution Insights Aggregation Table

**Requirement**: Create aggregation table for fast analytics queries

**Table**: `evolution_insights` **Schema**: `memory_master` **Purpose**: Daily aggregated metrics per user for performance

**Columns Required**:

- `id`: UUID primary key
- `user_id`: UUID foreign key to users table
- `app_id`: UUID foreign key to apps table (nullable for user-level aggregation)
- `date`: DATE
- `total_operations`: INTEGER
- `add_operations`: INTEGER
- `update_operations`: INTEGER
- `delete_operations`: INTEGER
- `noop_operations`: INTEGER
- `learning_efficiency`: FLOAT - (UPDATE + DELETE) / total_operations
- `conflict_resolution_count`: INTEGER - DELETE operations count
- `average_confidence`: FLOAT - average confidence score
- `average_similarity`: FLOAT - average similarity score
- `created_at`: TIMESTAMP WITH TIME ZONE
- `updated_at`: TIMESTAMP WITH TIME ZONE

**Indexes Required**:

- `idx_insights_user_date` ON (user_id, date)
- `idx_insights_app_date` ON (app_id, date) WHERE app_id IS NOT NULL
- `idx_insights_date` ON (date)

**Constraints Required**:

- `UNIQUE(user_id, app_id, date)` - one record per user/app/date

#### 2.3 Database Migration Requirements

**Migration File**: `api/alembic/versions/add_evolution_intelligence_tables.py` **Requirements**:

- Create both tables with proper schema, columns, indexes, constraints
- Add foreign key constraints with proper ON DELETE behavior
- Include rollback functionality
- Ensure migration is idempotent (can be run multiple times safely)

### 3. Evolution Operation Processing

#### 3.1 Memory Service Enhancement

**File**: `api/app/memory_service.py` **Class**: `MemoryService` **Method**: New method `add_memory_with_evolution()`

**Requirements**:

- Replace basic `memory_client.add()` call with evolution-aware processing
- Extract evolution statistics from mem0 response
- Store evolution operations in `evolution_operations` table
- Update aggregation data in `evolution_insights` table
- Maintain transaction integrity with existing chunking logic
- Preserve all existing error handling and retry mechanisms

**Evolution Statistics Extraction Requirements**:

- Parse mem0 response structure to identify operations performed
- Extract: operation_type, candidate_fact, confidence, reasoning
- Handle both single operations and batch operations (chunked text)
- Map mem0 memory IDs to database memory IDs
- Calculate similarity scores if available in response

#### 3.2 Evolution Transaction Integration

**File**: `api/app/memory_transaction.py` **Class**: `MemoryTransaction` **Method Enhancement**: `commit()`

**Requirements**:

- Extend existing transaction logic to include evolution tracking
- Store evolution operations atomically with memory operations
- Rollback evolution records if memory transaction fails
- Maintain existing transaction behavior for backward compatibility
- Log evolution statistics for each chunk in multi-chunk transactions

#### 3.3 Evolution Metrics Processing

**Requirement**: Create service for evolution metrics calculation

**New File**: `api/app/services/evolution_service.py` **Class**: `EvolutionService`

**Methods Required**:

- `extract_evolution_stats(mem0_response)` - parse mem0 response for statistics
- `store_evolution_operation(user_id, app_id, operation_data)` - store single operation
- `update_daily_insights(user_id, app_id, date)` - update aggregation table
- `get_evolution_metrics(user_id, timeframe)` - retrieve analytics
- `get_learning_efficiency(user_id, timeframe)` - calculate learning metrics

### 4. Enhanced MCP Tools

#### 4.1 Evolution Metrics Tool

**Tool Name**: `get_evolution_metrics` **Description**: "Get evolution intelligence metrics and learning efficiency for the user"

**Parameters Required**:

- `timeframe`: str = "week" (options: "day", "week", "month", "year")
- `app_filter`: str = None (optional app filter)

**Response Format Required**:

```
Evolution Intelligence Metrics (week):
• Learning Efficiency: 67.3% (intelligent operations vs basic ADD)
• Operations: 45 ADD, 23 UPDATE, 12 DELETE, 8 NOOP  
• Conflict Resolution: 12 contradictions automatically resolved
• Memory Quality: 89.2% average confidence score
• Top Evolution Categories: Programming Languages (8), Frameworks (5), Tools (3)
```

**Implementation Requirements**:

- Query `evolution_insights` table for fast aggregation
- Fall back to `evolution_operations` table for detailed breakdown
- Calculate learning efficiency as (UPDATE + DELETE) / total_operations
- Include confidence metrics and operation distribution
- Handle date range filtering with proper time zone handling

#### 4.2 Learning Insights Tool

**Tool Name**: `get_learning_insights`  
**Description**: "Get personalized learning insights and memory evolution patterns"

**Parameters Required**:

- `include_categories`: bool = True (include category breakdown)
- `include_trends`: bool = True (include time-based trends)

**Response Format Required**:

```
Learning Insights for User:
• Memory Evolution Efficiency: 72.1% (above average)
• Recent Evolution Activity: 15 operations in last 7 days
• Most Active Categories: Programming (8), Frameworks (4), DevOps (2)
• Learning Patterns: Strong technology adoption (95% successful migrations)
• Conflict Resolution: Excellent (90% accuracy in preference updates)
• Recommendation: Memory system effectively tracking your technical growth
```

**Implementation Requirements**:

- Analyze evolution patterns across time periods
- Compare user metrics to system averages
- Identify learning patterns and trends
- Provide actionable insights about memory effectiveness
- Include category-based analysis using existing categorization system

#### 4.3 Evolution Monitor Tool

**Tool Name**: `get_evolution_monitor` **Description**: "Monitor real-time evolution activity and system intelligence status"

**Parameters Required**:

- `limit`: int = 10 (number of recent operations to show)
- `operation_filter`: str = None (filter by ADD/UPDATE/DELETE/NOOP)

**Response Format Required**:

```
Recent Evolution Activity:
• 2m ago: UPDATE - Enhanced Python skill level (confidence: 92%)
• 5m ago: DELETE - Removed outdated React preference (conflict resolved)
• 12m ago: ADD - New Docker experience stored (confidence: 87%)
• 15m ago: NOOP - Redundant JavaScript mention ignored

System Intelligence Status:
• Evolution Engine: Active and healthy
• Custom Prompts: Technical domain optimized  
• Conflict Resolution: 3 contradictions resolved today
• Learning Rate: 12 intelligent operations in last hour
```

**Implementation Requirements**:

- Query recent `evolution_operations` records
- Show operation types with human-readable descriptions
- Include confidence scores and reasoning when available
- Display system health from existing health monitoring
- Real-time metrics on evolution processing effectiveness

### 5. Configuration Management Enhancement

#### 5.1 Admin Interface Integration

**Requirement**: Extend existing configuration management for evolution prompts

**File**: Admin/configuration interface (if exists) **Purpose**: Allow runtime configuration of custom prompts without code changes

**Features Required**:

- Text area inputs for custom_fact_extraction_prompt
- Text area inputs for custom_update_memory_prompt
- Prompt validation (JSON format checking for fact extraction)
- Preview functionality to test prompts
- Rollback capability to restore default prompts
- Hot reload support using existing configuration monitoring

#### 5.2 Environment Variable Support

**Requirement**: Support environment variable overrides for prompts

**Environment Variables**:

- `MEM0_CUSTOM_FACT_EXTRACTION_PROMPT`
- `MEM0_CUSTOM_UPDATE_MEMORY_PROMPT`

**Implementation**: Extend existing `_parse_environment_variables()` function in `memory.py`

### 6. Testing Requirements

#### 6.1 Unit Tests

**New Test File**: `api/tests/test_evolution_intelligence.py`

**Test Categories Required**:

- **Prompt Configuration Tests**: Verify custom prompts loaded correctly
- **Evolution Extraction Tests**: Test parsing mem0 responses for evolution stats
- **Database Operation Tests**: Test evolution_operations and evolution_insights tables
- **Metrics Calculation Tests**: Test learning efficiency and analytics calculations
- **Error Handling Tests**: Test graceful degradation when evolution tracking fails

**Test Coverage Requirements**:

- Minimum 85% code coverage for new evolution-related code
- Integration tests with existing memory transaction logic
- Performance tests for evolution metrics queries

#### 6.2 Integration Tests

**New Test File**: `api/tests/test_evolution_integration.py`

**Integration Scenarios Required**:

- **End-to-End Evolution**: Add memories and verify evolution operations tracked
- **Multi-Chunk Evolution**: Test evolution tracking with chunked text processing
- **MCP Tool Integration**: Test new MCP tools return correct evolution metrics
- **Configuration Hot Reload**: Test prompt updates reflected in processing
- **Degradation Scenarios**: Test evolution tracking when mem0 unavailable

#### 6.3 Performance Tests

**Enhancement**: Extend existing performance tests for evolution overhead

**Performance Requirements**:

- Evolution tracking adds <100ms to existing add_memories response time
- Evolution metrics queries complete in <200ms for 30-day timeframes
- Database aggregation updates complete in <50ms per operation
- Memory usage increase <10MB for evolution tracking features

### 7. Monitoring and Observability

#### 7.1 Health Check Enhancement

**File**: `api/app/health_service.py` **Enhancement**: Include evolution system health in existing health checks

**Health Metrics Required**:

- Evolution operation processing rate (operations/minute)
- Evolution database table sizes and growth rates
- Custom prompt configuration status
- Learning efficiency trends
- Error rates in evolution processing

#### 7.2 Logging Enhancement

**File**: `api/app/enhanced_logging.py` **Enhancement**: Add evolution-specific logging

**Log Events Required**:

- Evolution operation processing start/completion
- Custom prompt loading and validation
- Evolution metrics calculation timing
- Database aggregation update operations
- Evolution processing errors with context

#### 7.3 Metrics Integration

**Requirement**: Integrate evolution metrics with existing operation metrics system

**Metrics to Add**:

- `evolution_operations_total` (counter by operation_type)
- `evolution_processing_duration_seconds` (histogram)
- `learning_efficiency_percentage` (gauge by user)
- `evolution_confidence_score` (histogram)
- `evolution_database_operation_duration` (histogram)

### 8. Documentation Requirements

#### 8.1 API Documentation

**File**: Update existing API documentation  
**Content**: Document new MCP tools and their parameters **Format**: OpenAPI/Swagger format consistency with existing docs

#### 8.2 Configuration Documentation

**File**: `docs/EVOLUTION_CONFIGURATION.md` **Content**:

- Custom prompt configuration guide
- Technical domain prompt examples
- Troubleshooting evolution issues
- Performance tuning recommendations

#### 8.3 Migration Guide

**File**: `docs/EVOLUTION_MIGRATION.md` **Content**:

- Step-by-step upgrade process
- Database migration procedures
- Configuration migration steps
- Rollback procedures
- Compatibility notes

---

## Implementation Phases

### Phase 1: Foundation (Week 1-2)

1. Database schema creation and migration
2. Configuration system enhancement
3. Custom prompt integration
4. Basic evolution tracking implementation

### Phase 2: Analytics (Week 3-4)

1. Evolution service development
2. MCP tools implementation
3. Metrics calculation and aggregation
4. Health monitoring integration

### Phase 3: Testing & Polish (Week 5-6)

1. Comprehensive testing suite
2. Performance optimization
3. Documentation completion
4. Production deployment preparation

---

## Success Criteria

### Functional Requirements

- ✅ All custom prompts loaded and applied to mem0 processing
- ✅ Evolution operations (ADD/UPDATE/DELETE/NOOP) tracked in database
- ✅ New MCP tools provide evolution metrics and insights
- ✅ Learning efficiency calculation accurate and meaningful
- ✅ All existing MCP functionality preserved and unaffected

### Performance Requirements

- ✅ Evolution processing overhead <100ms per add_memories operation
- ✅ Evolution metrics queries <200ms response time
- ✅ Database storage impact <1GB for 100K operations
- ✅ Memory usage increase <10MB for evolution features

### Quality Requirements

- ✅ Learning efficiency >40% for technical conversations
- ✅ Evolution decision accuracy >85% based on manual review
- ✅ Custom prompt effectiveness >90% for technical content extraction
- ✅ System reliability maintained at 99.9% uptime with evolution features

### Integration Requirements

- ✅ Backward compatibility with all existing MCP clients
- ✅ Graceful degradation when evolution tracking unavailable
- ✅ Hot reload of custom prompts without service restart
- ✅ Seamless integration with existing transaction and chunking logic

---

## Risk Assessment

### Technical Risks

|Risk|Impact|Mitigation|
|---|---|---|
|mem0 prompt configuration complexity|High|Extensive testing with various prompt formats, fallback to defaults|
|Database performance with evolution tracking|Medium|Proper indexing, aggregation tables, query optimization|
|Increased memory usage from evolution features|Low|Memory monitoring, efficient data structures, cleanup procedures|
|Custom prompt effectiveness varies by domain|Medium|Domain-specific testing, prompt iteration based on feedback|

### Operational Risks

|Risk|Impact|Mitigation|
|---|---|---|
|Migration complexity for existing databases|Medium|Thorough testing, rollback procedures, staging environment validation|
|Custom prompt management complexity|Low|Clear documentation, validation tools, configuration interface|
|Evolution metrics interpretation difficulty|Medium|Clear documentation, example interpretations, user guidance|

### Business Risks

|Risk|Impact|Mitigation|
|---|---|---|
|User confusion about evolution features|Low|Clear communication, optional feature, gradual rollout|
|Performance impact on existing users|Medium|Performance testing, feature flags, gradual deployment|
|Increased system complexity|Medium|Comprehensive documentation, monitoring, support procedures|

---

## Conclusion

This enhancement leverages mem0's proven evolution intelligence to transform Memory Master v2 from basic storage into an intelligent, adaptive memory system. By configuring custom prompts rather than building evolution logic from scratch, the implementation reduces complexity while providing immediate benefits. The comprehensive analytics and monitoring provide visibility into memory evolution effectiveness, enabling continuous improvement and user insights.

The phased implementation approach ensures minimal risk while delivering incremental value. All requirements are designed to preserve existing functionality while adding powerful new capabilities for technical domain conversations.