# Memory Master v2: Supabase Foundation Architecture Plan

This plan outlines the design for migrating Memory Master v2 to Supabase, covering schema architecture, database migration, authentication, configuration, and a dual-write system. The goal is to ensure maintainability, backward compatibility, and zero-downtime migration.

## 1. Supabase Schema Architecture

**Goal:** Design the `memory_master` schema structure for complete isolation and map all existing SQLAlchemy models to PostgreSQL, including User model extensions and RLS policies.

**Steps:**

1.  **Schema Definition:**
    *   Create a dedicated schema named `memory_master` in Supabase.
    *   Set appropriate schema-level permissions to ensure isolation from other applications.

2.  **Table Mapping (DDL Generation):**
    *   For each of the 10 existing SQLAlchemy models (`users`, `apps`, `memories`, `categories`, `memory_categories`, `configs`, `access_controls`, `archive_policies`, `memory_status_history`, `memory_access_logs`), generate the corresponding PostgreSQL DDL (Data Definition Language) statements.
    *   Ensure all column names, data types, and constraints (NOT NULL, UNIQUE) are accurately translated from SQLAlchemy to PostgreSQL.
    *   Specifically handle `JSON` (map to `jsonb`), `DateTime` (map to `timestamp with time zone`), `UUIDs` (map to `uuid`), and `Enums` (create custom PostgreSQL `ENUM` types).
    *   Preserve all existing UUID primary keys.

3.  **User Model Extension:**
    *   Modify the `users` table within the `memory_master` schema to include:
        *   `supabase_user_id` (UUID, NOT NULL, UNIQUE): This will be a foreign key referencing `auth.users.id`.
        *   `email_verified` (BOOLEAN, DEFAULT FALSE): To track email verification status from Supabase Auth.
        *   `last_sign_in_at` (TIMESTAMP WITH TIME ZONE): To track the last sign-in time from Supabase Auth.
    *   Plan for a one-time migration script to populate `supabase_user_id` for existing users by linking them to newly created Supabase Auth users.

4.  **Row Level Security (RLS) Policies:**
    *   Enable RLS on all tables within the `memory_master` schema.
    *   Design RLS policies to ensure each authenticated user can only access their own data. This will typically involve policies like:
        *   `CREATE POLICY "Enable read access for authenticated users" ON memory_master.memories FOR SELECT USING (auth.uid() = user_id);`
        *   Similar policies for `apps`, `configs`, `access_controls`, `archive_policies`, `memory_status_history`, `memory_access_logs` based on `user_id` or `supabase_user_id`.
        *   For junction tables like `memory_categories`, policies will need to consider the `user_id` from the related `memories` table.

5.  **Schema-Level Permissions:**
    *   Define PostgreSQL roles (e.g., `memory_master_app_user`, `memory_master_admin`).
    *   Grant specific permissions (SELECT, INSERT, UPDATE, DELETE) on the `memory_master` schema and its tables to these roles.
    *   Ensure the application's database connection uses a role with appropriate, least-privilege access.

**Mermaid Diagram: Supabase Schema Architecture**

```mermaid
graph TD
    subgraph Supabase Project
        direction LR
        A[auth.users] --> B[memory_master.users]
        subgraph memory_master Schema
            direction TB
            B(users)
            C(apps)
            D(memories)
            E(categories)
            F(memory_categories)
            G(configs)
            H(access_controls)
            I(archive_policies)
            J(memory_status_history)
            K(memory_access_logs)

            B -- FK: supabase_user_id --> A
            D -- FK: user_id --> B
            C -- FK: user_id --> B
            F -- FK: memory_id --> D
            F -- FK: category_id --> E
            G -- FK: user_id --> B
            H -- FK: user_id --> B
            I -- FK: user_id --> B
            J -- FK: memory_id --> D
            K -- FK: memory_id --> D
            K -- FK: user_id --> B
        end
    end

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style B fill:#bbf,stroke:#333,stroke-width:2px
    style C fill:#bbf,stroke:#333,stroke-width:2px
    style D fill:#bbf,stroke:#333,stroke-width:2px
    style E fill:#bbf,stroke:#333,stroke-width:2px
    style F fill:#bbf,stroke:#333,stroke-width:2px
    style G fill:#bbf,stroke:#333,stroke-width:2px
    style H fill:#bbf,stroke:#333,stroke-width:2px
    style I fill:#bbf,stroke:#333,stroke-width:2px
    style J fill:#bbf,stroke:#333,stroke-width:2px
    style K fill:#bbf,stroke:#333,stroke-width:2px
```

## 2. Database Migration Strategy

**Goal:** Design a robust data type mapping, preserve foreign key relationships, plan index migration, and integrate with Alembic for schema evolution.

**Steps:**

1.  **Data Type Mapping:**
    *   **UUIDs:** SQLite `TEXT` (for UUID strings) to PostgreSQL `UUID`.
    *   **JSON:** SQLite `TEXT` (for JSON strings) to PostgreSQL `JSONB`.
    *   **DateTime:** SQLite `TEXT` (ISO 8601 strings) to PostgreSQL `TIMESTAMP WITH TIME ZONE`.
    *   **Enums:** SQLite `TEXT` to PostgreSQL custom `ENUM` types (e.g., `CREATE TYPE memory_state AS ENUM ('active', 'paused', 'archived', 'deleted');`).
    *   Standard types (INTEGER, TEXT, BOOLEAN) will map directly.

2.  **Foreign Key Relationship Preservation:**
    *   During DDL generation, explicitly define `FOREIGN KEY` constraints with `ON DELETE CASCADE` or `ON DELETE RESTRICT` as per existing SQLAlchemy model definitions.
    *   Ensure that the order of table creation during migration respects foreign key dependencies (e.g., `users` before `memories`).

3.  **Index Migration Strategy:**
    *   Identify all existing indexes defined in SQLAlchemy models.
    *   Translate these to PostgreSQL `CREATE INDEX` statements.
    *   Consider adding new indexes for frequently queried columns, especially those used in `WHERE` clauses or `JOIN` conditions, to optimize performance on PostgreSQL.
    *   Specifically, ensure the `vector` field in the `memories` table has an appropriate index for Qdrant integration (e.g., a `pg_vector` index if using that extension).

4.  **Alembic Integration:**
    *   Configure Alembic to manage migrations for the new PostgreSQL database and `memory_master` schema.
    *   Update `alembic.ini` and `api/alembic/env.py` to connect to Supabase PostgreSQL.
    *   Generate initial migration scripts for the `memory_master` schema based on the SQLAlchemy models.
    *   Future schema changes will be managed via Alembic, ensuring version control and reproducibility.

**Mermaid Diagram: Database Migration Flow**

```mermaid
graph TD
    A[SQLite Database] --> B{Data Extraction & Transformation}
    B --> C[PostgreSQL Data Types & Schema]
    C --> D[Supabase PostgreSQL]
    D -- Indexes & FKs --> D
    E[SQLAlchemy Models] --> F[Alembic]
    F --> D

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style B fill:#bbf,stroke:#333,stroke-width:2px
    style C fill:#bbf,stroke:#333,stroke-width:2px
    style D fill:#bbf,stroke:#333,stroke-width:2px
    style E fill:#f9f,stroke:#333,stroke-width:2px
    style F fill:#bbf,stroke:#333,stroke-width:2px
```

## 3. Authentication Architecture

**Goal:** Integrate Supabase Auth with the existing User model, ensure backward compatibility, design JWT validation middleware for FastAPI, and plan user context injection.

**Steps:**

1.  **Supabase Auth Integration:**
    *   **Frontend (Next.js):**
        *   Implement `LoginForm`, `SignupForm`, `AuthProvider`, and `ProtectedRoute` components using Supabase client-side SDK.
        *   Ensure all UI components adhere to existing Tailwind CSS theme and custom properties.
        *   Utilize existing UI patterns for form validation, loading states, toast notifications, and dark/light mode.
    *   **Backend (FastAPI):**
        *   Use Supabase Python client library for server-side authentication.
        *   Implement logic to auto-create internal `memory_master.users` records when a new user signs up via Supabase Auth.
        *   Map `auth.users.id` to `memory_master.users.supabase_user_id`.

2.  **Backward Compatibility for `user_id`:**
    *   During the dual-write phase, existing API endpoints will continue to function with the current `user_id` system (likely a string-based identifier).
    *   New authenticated requests will use the Supabase JWT to identify the user.
    *   A mapping mechanism will be in place to translate between the legacy `user_id` and the new `supabase_user_id` where necessary, especially for existing data.

3.  **JWT Validation Middleware (FastAPI):**
    *   Create a FastAPI middleware that intercepts incoming requests.
    *   Extract the JWT from the `Authorization: Bearer` header.
    *   Validate the JWT using `SUPABASE_JWT_SECRET`.
    *   If valid, decode the token to extract `auth.uid()` (Supabase user ID) and other user claims.
    *   Inject the authenticated user's context (e.g., `supabase_user_id`, `email`) into the request state (e.g., `request.state.user`).
    *   Handle invalid or expired tokens by returning appropriate HTTP 401/403 responses.
    *   Implement a fallback mechanism for unauthenticated requests during the migration phase, allowing them to proceed with a "default" or "legacy" user context if `ENABLE_AUTHENTICATION` feature flag is off or `LEGACY_MODE` is on.

4.  **User Context Injection:**
    *   Modify existing FastAPI endpoints to optionally accept an authenticated user context.
    *   If a user is authenticated, use `request.state.user.supabase_user_id` for data operations and RLS enforcement.
    *   If not authenticated (and `LEGACY_MODE` is active), continue to use the existing `user_id` logic or a predefined default user.
    *   Ensure existing API response formats and structures are preserved.

**Mermaid Diagram: Authentication Flow**

```mermaid
graph TD
    subgraph Frontend (Next.js)
        A[User Login/Signup] --> B[Supabase Client SDK]
        B -- JWT --> C[Supabase Auth Service]
        C -- Session/User Data --> B
        B -- Auth Context --> D[AuthProvider]
        D -- Protected Routes --> E[ProtectedRoute]
    end

    subgraph Backend (FastAPI)
        F[Incoming Request] --> G[JWT Validation Middleware]
        G -- Valid JWT --> H[User Context Injection]
        H -- Authenticated Request --> I[API Endpoints]
        G -- Invalid/No JWT (Legacy Mode) --> I
        I -- Data Access --> J[Supabase PostgreSQL]
    end

    C -- User Creation/Auth --> J

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style B fill:#bbf,stroke:#333,stroke-width:2px
    style C fill:#f9f,stroke:#333,stroke-width:2px
    style D fill:#bbf,stroke:#333,stroke-width:2px
    style E fill:#bbf,stroke:#333,stroke-width:2px
    style F fill:#f9f,stroke:#333,stroke-width:2px
    style G fill:#bbf,stroke:#333,stroke-width:2px
    style H fill:#bbf,stroke:#333,stroke-width:2px
    style I fill:#bbf,stroke:#333,stroke-width:2px
    style J fill:#f9f,stroke:#333,stroke-width:2px
```

## 4. Configuration and Environment Design

**Goal:** Define new environment variables, design a feature flag system for migration control, and plan database connection architecture.

**Steps:**

1.  **Environment Variables Structure:**
    *   **Supabase Connection:**
        *   `SUPABASE_URL`: Supabase project URL (e.g., `https://<project-ref>.supabase.co`)
        *   `SUPABASE_ANON_KEY`: Public `anon` key for client-side authentication.
        *   `SUPABASE_SERVICE_ROLE_KEY`: Service role key for server-side admin operations (e.g., creating users, bypassing RLS for specific tasks).
        *   `SUPABASE_JWT_SECRET`: JWT secret for validating Supabase tokens locally.
    *   **Migration Control:**
        *   `MIGRATION_MODE`: (`sqlite_only` | `dual_write` | `supabase_only`) - Controls where data is written.
        *   `ENABLE_AUTHENTICATION`: (`true` | `false`) - Feature flag to enable/disable the new authentication system.
        *   `LEGACY_MODE`: (`true` | `false`) - Flag for backward compatibility, allowing unauthenticated access to existing APIs.
        *   `MIGRATION_BATCH_SIZE`: Integer for controlling data migration batch size.
    *   **Database Configuration:**
        *   `SUPABASE_SCHEMA`: Set to `memory_master`.
        *   `DATABASE_SEARCH_PATH`: PostgreSQL `search_path` configuration (e.g., `public, memory_master`).
        *   `CONNECTION_POOL_SIZE`: Integer for database connection pooling.
        *   `QUERY_TIMEOUT`: Integer for database query timeout settings.

2.  **Feature Flag System:**
    *   Implement a simple feature flag system using environment variables.
    *   The `MIGRATION_MODE`, `ENABLE_AUTHENTICATION`, and `LEGACY_MODE` variables will act as primary flags.
    *   The application's `config.py` will load these variables and provide helper functions to check their status.
    *   Frontend components will conditionally render based on `ENABLE_AUTHENTICATION` and `LEGACY_MODE`.
    *   Backend logic (e.g., dual-write, authentication middleware) will use `MIGRATION_MODE` and `ENABLE_AUTHENTICATION` to control behavior.

3.  **Database Connection Architecture:**
    *   Utilize a connection pooling library (e.g., `SQLAlchemy`'s built-in pooling or `asyncpg`'s pooling) for efficient management of connections to Supabase PostgreSQL.
    *   Configure the `search_path` for the PostgreSQL connection to prioritize the `memory_master` schema, allowing direct table access without prefixing (e.g., `SELECT * FROM users;` instead of `SELECT * FROM memory_master.users;`).
    *   Implement health checks for both SQLite (during dual-write) and Supabase connections.

**Mermaid Diagram: Configuration & Environment**

```mermaid
graph TD
    subgraph Environment Variables
        A[SUPABASE_URL]
        B[SUPABASE_ANON_KEY]
        C[SUPABASE_SERVICE_ROLE_KEY]
        D[SUPABASE_JWT_SECRET]
        E[MIGRATION_MODE]
        F[ENABLE_AUTHENTICATION]
        G[LEGACY_MODE]
        H[SUPABASE_SCHEMA]
        I[CONNECTION_POOL_SIZE]
    end

    subgraph Application
        J[Config Module] --> K[Feature Flag Logic]
        K --> L[Dual-Write Coordinator]
        K --> M[Auth Middleware]
        K --> N[Frontend UI]
        J --> O[Database Connection Pool]
        O --> P[Supabase PostgreSQL]
        O --> Q[SQLite Database]
    end

    A,B,C,D,H,I --> O
    E,F,G --> K
    K --> L,M,N
```

## 5. Dual-Write System Architecture

**Goal:** Design a dual-write coordinator for SQLite to Supabase transition, handle write failures, ensure data consistency, and plan health checking.

**Steps:**

1.  **Dual-Write Coordinator:**
    *   Implement a central `WriteCoordinator` module in the FastAPI application.
    *   This coordinator will be responsible for routing write operations based on the `MIGRATION_MODE` feature flag.
    *   When `MIGRATION_MODE` is `dual_write`:
        *   Write to SQLite first.
        *   If SQLite write is successful, then write to Supabase.
        *   If SQLite write fails, log the error and do not attempt Supabase write.
        *   If Supabase write fails, log the error and proceed (SQLite is primary).

2.  **Write Failure Handling and Retry Logic:**
    *   Implement robust error logging for both SQLite and Supabase write operations.
    *   For Supabase write failures during dual-write, implement a configurable retry mechanism (e.g., exponential backoff) for transient errors.
    *   For persistent Supabase write failures, log the discrepancy and potentially queue the failed write for later manual reconciliation.
    *   Alerting mechanisms will be set up for critical write failures.

3.  **Data Consistency Validation:**
    *   Implement a background job or a separate utility script to periodically compare data between SQLite and Supabase.
    *   This validation will involve:
        *   Comparing row counts for all tables.
        *   Sampling records and comparing their content (e.g., hash of row data).
        *   Verifying foreign key relationships in Supabase.
    *   Report any discrepancies for manual investigation and correction.

4.  **Health Checking for Both Database Connections:**
    *   Implement health check endpoints in the FastAPI application that verify connectivity to both SQLite and Supabase.
    *   These checks will:
        *   Attempt a simple read query (e.g., `SELECT 1;`) on each database.
        *   Report status (healthy/unhealthy) and latency.
    *   Integrate these health checks with monitoring systems.

**Mermaid Diagram: Dual-Write System**

```mermaid
graph TD
    subgraph FastAPI Application
        A[API Endpoint (Write Request)] --> B{WriteCoordinator}
        B -- MIGRATION_MODE = sqlite_only --> C[SQLite DB]
        B -- MIGRATION_MODE = dual_write --> D[SQLite DB]
        D -- Success --> E[Supabase DB]
        E -- Failure --> F[Error Log / Retry Queue]
        B -- MIGRATION_MODE = supabase_only --> G[Supabase DB]
    end

    subgraph Background Processes
        H[Data Consistency Validator] --> I[SQLite DB]
        H --> J[Supabase DB]
        H -- Discrepancies --> K[Alerts / Reports]
    end

    subgraph Monitoring
        L[Health Check Endpoint] --> M[SQLite DB]
        L --> N[Supabase DB]
        L -- Status --> O[Monitoring System]
    end

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style B fill:#bbf,stroke:#333,stroke-width:2px
    style C fill:#f9f,stroke:#333,stroke-width:2px
    style D fill:#f9f,stroke:#333,stroke-width:2px
    style E fill:#f9f,stroke:#333,stroke-width:2px
    style F fill:#bbf,stroke:#333,stroke-width:2px
    style G fill:#f9f,stroke:#333,stroke-width:2px
    style H fill:#bbf,stroke:#333,stroke-width:2px
    style I fill:#f9f,stroke:#333,stroke-width:2px
    style J fill:#f9f,stroke:#333,stroke-width:2px
    style K fill:#bbf,stroke:#333,stroke-width:2px
    style L fill:#bbf,stroke:#333,stroke-width:2px
    style M fill:#f9f,stroke:#333,stroke-width:2px
    style N fill:#f9f,stroke:#333,stroke-width:2px
    style O fill:#bbf,stroke:#333,stroke-width:2px
```

### Risk Assessment and Mitigation Strategies

1.  **Data Loss/Corruption during Migration:**
    *   **Risk:** Incorrect data type mapping, failed foreign key preservation, or interrupted migration.
    *   **Mitigation:**
        *   Comprehensive pre-migration validation (SQLite integrity checks, schema comparison).
        *   Full backup of SQLite database before starting migration.
        *   Batch migration with validation after each batch.
        *   Robust error handling and logging during migration.
        *   Rollback procedures and validation scripts.

2.  **Downtime during Transition:**
    *   **Risk:** Service interruption during database cutover or authentication system switch.
    *   **Mitigation:**
        *   Dual-write system for zero-downtime writes.
        *   Feature flags for gradual rollout of authentication and read shifts.
        *   Fallback mechanisms for reads (initially read from SQLite, then shift to Supabase with SQLite fallback).
        *   Thorough testing in staging environments.

3.  **Performance Degradation:**
    *   **Risk:** New PostgreSQL queries are slower, or connection pooling is misconfigured.
    *   **Mitigation:**
        *   Careful index migration and addition of new indexes for PostgreSQL.
        *   Performance comparison testing between SQLite and Supabase.
        *   Proper connection pooling configuration and monitoring.
        *   Database query timeout settings to prevent long-running queries.

4.  **Backward Compatibility Issues:**
    *   **Risk:** Existing API endpoints or MCP integration breaks due to authentication changes or schema differences.
    *   **Mitigation:**
        *   Optional authentication headers and fallback to default user for unauthenticated requests.
        *   `LEGACY_MODE` feature flag to control backward compatibility behavior.
        *   Comprehensive regression testing for all existing API endpoints.
        *   User context injection designed to be non-breaking for existing API contracts.

5.  **Security Vulnerabilities:**
    *   **Risk:** Improper RLS policies, exposed service role keys, or insecure JWT handling.
    *   **Mitigation:**
        *   Strict RLS policies enabled on all tables.
        *   Least-privilege principle for database roles and application connections.
        *   Secure handling of environment variables (e.g., not committing to VCS).
        *   Robust JWT validation (signature, expiration, audience).
        *   Regular security audits and penetration testing.