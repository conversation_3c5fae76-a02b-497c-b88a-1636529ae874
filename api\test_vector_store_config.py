#!/usr/bin/env python3
"""
Test script to diagnose vector store configuration and custom prompt issues.
"""

import os
import sys
import json
import traceback
from pathlib import Path

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv()

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

def test_environment_variables():
    """Test that required environment variables are set."""
    print("=== Environment Variables ===")
    required_vars = ['OPENAI_API_KEY', 'SUPABASE_DATABASE_URL']
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            # Mask sensitive values
            if 'key' in var.lower() or 'secret' in var.lower():
                masked = value[:8] + "..." + value[-4:] if len(value) > 12 else "***"
                print(f"✓ {var}: {masked}")
            else:
                print(f"✓ {var}: {value}")
        else:
            print(f"✗ {var}: NOT SET")
    print()

def test_qdrant_connectivity():
    """Test Qdrant vector store connectivity."""
    print("=== Qdrant Connectivity ===")
    try:
        import requests
        response = requests.get("http://localhost:6333/collections", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Qdrant is accessible")
            print(f"  Collections: {data.get('result', {}).get('collections', [])}")
        else:
            print(f"✗ Qdrant returned status {response.status_code}")
    except Exception as e:
        print(f"✗ Qdrant connectivity failed: {e}")
    print()

def test_mem0_configuration():
    """Test mem0 configuration and initialization."""
    print("=== mem0 Configuration ===")
    try:
        from app.utils.memory import get_memory_client
        from app.utils.config_manager import get_config_manager
        
        # Get current configuration
        config_manager = get_config_manager()
        config = config_manager.get_config()
        
        print("Current configuration:")
        print(f"  mem0 version: {config.get('mem0', {}).get('version', 'not set')}")
        print(f"  LLM provider: {config.get('mem0', {}).get('llm', {}).get('provider', 'not set')}")
        print(f"  Embedder provider: {config.get('mem0', {}).get('embedder', {}).get('provider', 'not set')}")
        
        # Check custom prompts
        custom_fact = config.get('mem0', {}).get('custom_fact_extraction_prompt')
        custom_update = config.get('mem0', {}).get('custom_update_memory_prompt')
        
        print(f"  Custom fact extraction prompt: {'SET' if custom_fact else 'NOT SET'}")
        print(f"  Custom update memory prompt: {'SET' if custom_update else 'NOT SET'}")
        
        # Try to get memory client
        print("\nTesting memory client initialization...")
        client = get_memory_client()
        print("✓ Memory client initialized successfully")
        
        # Test basic operations
        print("\nTesting basic operations...")
        
        # Test add operation
        test_memory = "This is a test memory for vector store connectivity"
        result = client.add(test_memory, user_id="test_user")
        print(f"✓ Add operation successful: {result.get('id', 'no id')}")
        
        # Test search operation
        search_results = client.search("test memory", user_id="test_user", limit=1)
        print(f"✓ Search operation successful: found {len(search_results)} results")
        
        # Clean up test memory
        if result.get('id'):
            try:
                client.delete(result['id'])
                print("✓ Cleanup successful")
            except Exception as e:
                print(f"⚠ Cleanup failed: {e}")
                
    except Exception as e:
        print(f"✗ mem0 configuration test failed: {e}")
        traceback.print_exc()
    print()

def test_custom_prompts():
    """Test custom prompt loading."""
    print("=== Custom Prompts ===")
    try:
        from app.utils.evolution_prompts import get_default_technical_prompts
        
        prompts = get_default_technical_prompts()
        
        fact_prompt = prompts.get('custom_fact_extraction_prompt')
        update_prompt = prompts.get('custom_update_memory_prompt')
        
        print(f"✓ Technical fact extraction prompt loaded: {len(fact_prompt) if fact_prompt else 0} characters")
        print(f"✓ Technical update memory prompt loaded: {len(update_prompt) if update_prompt else 0} characters")
        
        if fact_prompt:
            print(f"  Fact prompt preview: {fact_prompt[:100]}...")
        if update_prompt:
            print(f"  Update prompt preview: {update_prompt[:100]}...")
            
    except Exception as e:
        print(f"✗ Custom prompts test failed: {e}")
        traceback.print_exc()
    print()

def test_vector_store_health():
    """Test vector store health and degradation status."""
    print("=== Vector Store Health ===")
    try:
        from app.utils.memory import _memory_singleton
        
        # Check if in degraded mode
        if _memory_singleton._degraded_mode:
            print(f"⚠ System is in DEGRADED MODE")
            print(f"  Reason: {_memory_singleton._degradation_reason}")
            print(f"  Recovery attempts: {_memory_singleton._recovery_attempts}")
        else:
            print("✓ System is in NORMAL MODE")
        
        # Test connectivity
        is_connected = _memory_singleton._check_vector_store_connectivity()
        print(f"Vector store connectivity: {'✓ CONNECTED' if is_connected else '✗ DISCONNECTED'}")
        
        # Check connectivity history
        history = list(_memory_singleton._connectivity_history)
        if history:
            recent_checks = history[-5:]  # Last 5 checks
            print(f"Recent connectivity checks: {len(recent_checks)} entries")
            for timestamp, connected in recent_checks:
                status = "✓" if connected else "✗"
                print(f"  {status} {timestamp}")
        
    except Exception as e:
        print(f"✗ Vector store health test failed: {e}")
        traceback.print_exc()
    print()

def main():
    """Run all diagnostic tests."""
    print("Vector Store Configuration Diagnostic Tool")
    print("=" * 50)
    
    test_environment_variables()
    test_qdrant_connectivity()
    test_mem0_configuration()
    test_custom_prompts()
    test_vector_store_health()
    
    print("Diagnostic complete!")

if __name__ == "__main__":
    main()
