Memory Master v2: Supabase Authentication & Database Migration
Product Requirements Document (PRD)
Version: 2.0
Date: June 14, 2025
Project: Memory Master v2 Enhancement
Document Owner: Development Team
Development Target: LLM-Assisted Implementation

📋 Executive Summary
This PRD outlines the implementation of Supabase password-based authentication and migration from SQLite to Supabase PostgreSQL using a dedicated memory_master schema for Memory Master v2. All requirements are structured for LLM-assisted development with detailed feature specifications and implementation guidance.
Key Objectives

Enable multi-user support with secure Supabase authentication
Migrate to dedicated memory_master schema in shared Supabase instance
Maintain 100% backward compatibility during transition
Preserve all existing API functionality and MCP integration
Ensure UI consistency with existing themes and design patterns


🎯 Problem Statement
Current State

Single-user system: No authentication mechanism
Local SQLite database: Limited to single instance, stored in /api/openmemory.db
No cloud synchronization: Data isolated to local environment
Scalability limitations: SQLite constraints for concurrent access
Shared Supabase environment: Need proper namespace isolation

Target State

Multi-user authentication: Secure user management via Supabase Auth
Dedicated schema: memory_master schema in shared Supabase PostgreSQL
Cross-device synchronization: Access memories from anywhere
Enhanced security: User isolation with schema-level boundaries
Namespace isolation: Clean separation from other applications


🏗️ Solution Overview
Dedicated Schema Architecture
Implement memory_master schema containing all 10 existing tables (users, apps, memories, categories, memory_categories, configs, access_controls, archive_policies, memory_status_history, memory_access_logs) with complete namespace isolation from other applications sharing the Supabase instance.
Authentication-First Approach
Implement Supabase Auth with email/password authentication while maintaining backward compatibility for existing MCP integration and API endpoints.
Zero-Downtime Migration Strategy
Utilize dual-write approach and feature flags to ensure seamless transition from SQLite to dedicated Supabase schema without service interruption.

📊 Technical Requirements & LLM Development Instructions
1. Database Schema Architecture
1.1 Dedicated Schema Implementation

Schema Name: memory_master
Purpose: Complete isolation from other applications in shared Supabase instance
Tables to Migrate: All 10 existing SQLAlchemy models from current app/models.py

users - User management with Supabase integration
apps - Application contexts for memory organization
memories - Core memory storage with vector integration
categories - Memory categorization system
memory_categories - Junction table for many-to-many relationships
configs - Application configuration storage
access_controls - Granular permission management
archive_policies - Automated memory lifecycle management
memory_status_history - Audit trail for memory state changes
memory_access_logs - Access tracking and analytics



1.2 Schema-Level Security Implementation

Row Level Security (RLS): Enable on all tables within memory_master schema
User Isolation: Each authenticated user can only access their own data
Schema Permissions: Grant specific roles access only to memory_master schema
Cross-Schema Integration: Create controlled views for other applications if needed

1.3 Enhanced User Model Integration
Instructions for LLM: Extend existing User model with Supabase-specific fields:

Add supabase_user_id UUID field linking to auth.users(id)
Add email_verified boolean field for email verification status
Add last_sign_in_at timestamp for session tracking
Ensure backward compatibility with existing user records
Create migration logic to map existing users to Supabase auth users

2. Authentication System Implementation
2.1 Frontend Authentication Components (Next.js)
Instructions for LLM: Create authentication system matching existing UI theme and color palette:
Theme Consistency Requirements:

Use existing color scheme from tailwind.config.ts
Match current component styling patterns from components/ directory
Maintain consistency with existing button styles, form inputs, and layouts
Follow current typography and spacing conventions
Preserve existing dark/light mode support using next-themes

Required Authentication Components:

LoginForm Component: Email/password login with form validation

Use existing form styling patterns from current components
Include error handling with toast notifications (using existing Sonner setup)
Add loading states with existing spinner/loading patterns
Implement "Remember me" functionality
Add "Forgot password" link with proper styling


SignupForm Component: User registration with email verification

Match LoginForm styling and validation patterns
Include password strength indicator using existing UI patterns
Add terms of service and privacy policy checkboxes
Implement email verification flow with success/error states


AuthProvider Component: Context provider for authentication state

Wrap entire application to provide auth state globally
Handle automatic token refresh seamlessly
Manage user session persistence across browser sessions
Provide loading states during authentication checks


ProtectedRoute Component: Route protection middleware

Redirect unauthenticated users to login page
Show loading spinner during authentication verification
Handle auth state changes gracefully
Maintain redirect functionality after successful login



2.2 Backend Authentication Middleware (FastAPI)
Instructions for LLM: Implement authentication middleware that maintains API compatibility:
JWT Validation Middleware:

Create middleware to validate Supabase JWT tokens
Extract user information from valid tokens
Handle token expiration and refresh scenarios
Provide fallback for existing MCP integration (allow unauthenticated access with feature flags)

User Management Integration:

Auto-create internal user records when new Supabase user authenticates
Map Supabase user IDs to internal user system
Handle user profile updates from Supabase auth changes
Maintain existing user data structure and relationships

API Endpoint Enhancement:

Add optional authentication to all existing endpoints
Maintain backward compatibility for unauthenticated requests during transition
Implement user context injection for authenticated requests
Preserve existing API response formats and structures

3. Database Migration System
3.1 Migration Architecture
Instructions for LLM: Create comprehensive migration system with these components:
Pre-Migration Validation:

Verify SQLite database integrity and structure
Check for data consistency and foreign key relationships
Create complete backup of existing SQLite database
Validate Supabase connection and schema creation permissions

Schema Creation Process:

Create memory_master schema in Supabase
Set appropriate schema permissions and ownership
Create all tables with exact field mappings from SQLAlchemy models
Implement all indexes, constraints, and foreign key relationships
Set up Row Level Security policies for user isolation

Data Migration Process:

Migrate data in batches to handle large datasets
Preserve all UUIDs and maintain referential integrity
Handle special data types (JSON, DateTime, Enums)
Validate each batch after migration
Provide progress reporting and error handling

Post-Migration Validation:

Compare row counts between SQLite and Supabase
Validate random sample of records for data integrity
Test all foreign key relationships
Verify index performance and query execution
Run existing test suite against migrated data

3.2 Dual-Write Implementation
Instructions for LLM: Implement dual-write system for zero-downtime migration:
Configuration Management:

Use feature flags to control write destinations
Support modes: sqlite_only, dual_write, supabase_only
Allow runtime switching between modes for gradual rollout
Implement health checks for both database connections

Write Coordination:

Write to SQLite first (primary), then Supabase (secondary) during transition
Handle write failures gracefully with retry logic
Log discrepancies between databases for investigation
Provide manual sync tools for data consistency

Read Strategy:

Initially read from SQLite during dual-write phase
Gradually shift reads to Supabase after validation
Implement fallback to SQLite if Supabase read fails
Monitor performance differences between databases

4. UI/UX Enhancement Requirements
4.1 Theme and Styling Consistency
Instructions for LLM: Ensure all new UI components match existing design system:
Color Palette Preservation:

Extract existing color scheme from current components
Use identical CSS custom properties and Tailwind classes
Maintain existing accent colors, background colors, and text colors
Preserve current hover states and active states

Component Pattern Matching:

Study existing component structure in components/ directory
Match existing button variants, sizes, and styling
Use same form input styling, validation states, and focus styles
Maintain consistent spacing, typography, and layout patterns

Navigation Integration:

Add authentication-related navigation items to existing menu structure
Show/hide menu items based on authentication state
Display user profile information in existing header/navbar
Add logout functionality with existing styling patterns

4.2 User Experience Flow
Instructions for LLM: Design authentication flows that feel native to existing application:
Onboarding Experience:

Create welcome flow for new users that matches existing UI patterns
Explain memory management concepts using existing content styling
Guide users through initial memory creation with consistent tooltips/help text
Use existing modal/dialog patterns for guided tours

Session Management UX:

Handle session expiration gracefully with non-intrusive notifications
Provide automatic session extension for active users
Show authentication status in existing status bar/footer
Handle network connectivity issues with existing error patterns

5. API Compatibility and Enhancement
5.1 Endpoint Preservation
Instructions for LLM: Maintain complete API compatibility during transition:
Existing Endpoint Structure:

Preserve all current endpoint paths and HTTP methods
Maintain existing request/response formats
Keep current pagination, filtering, and sorting parameters
Ensure MCP server integration continues working unchanged

Authentication Integration:

Add optional Authorization: Bearer <token> header support
Fall back to default user for requests without authentication
Implement user context switching based on token validation
Preserve existing error response formats and status codes

5.2 MCP Server Compatibility
Instructions for LLM: Ensure Claude MCP integration remains fully functional:
MCP Protocol Preservation:

Maintain existing MCP server endpoints and methods
Preserve current request/response schemas for Claude integration
Keep existing error handling and status reporting
Ensure backward compatibility with current Claude configuration

User Context for MCP:

Implement user identification for MCP requests
Allow MCP to work with default user during transition
Add optional user context header for MCP requests
Maintain existing memory retrieval and storage functionality

6. Configuration and Environment Management
6.1 Environment Variables Structure
Instructions for LLM: Implement comprehensive configuration system:
Supabase Configuration:

SUPABASE_URL: Supabase project URL
SUPABASE_ANON_KEY: Public anon key for client-side auth
SUPABASE_SERVICE_ROLE_KEY: Server-side key for admin operations
SUPABASE_JWT_SECRET: JWT verification secret

Migration Control:

MIGRATION_MODE: Control dual-write behavior (sqlite_only|dual_write|supabase_only)
ENABLE_AUTHENTICATION: Feature flag for authentication system
LEGACY_MODE: Backward compatibility mode for existing users
MIGRATION_BATCH_SIZE: Control migration batch size for performance

Database Configuration:

SUPABASE_SCHEMA: Set to memory_master for dedicated schema
DATABASE_SEARCH_PATH: PostgreSQL search path configuration
CONNECTION_POOL_SIZE: Database connection pool configuration
QUERY_TIMEOUT: Database query timeout settings

6.2 Feature Flag System
Instructions for LLM: Implement robust feature flag system:
Flag Categories:

Authentication Flags: Control auth system rollout
Database Flags: Control migration phases
UI Flags: Control new component visibility
Integration Flags: Control external service integration

Flag Implementation:

Use environment variables for flag configuration
Support runtime flag changes without restart
Implement flag-based component rendering
Add admin interface for flag management

7. Testing and Validation Requirements
7.1 Automated Testing Strategy
Instructions for LLM: Implement comprehensive testing for all new features:
Authentication Testing:

Unit tests for all authentication components
Integration tests for login/logout flows
Session management and token refresh testing
Error handling and edge case validation

Migration Testing:

Data integrity validation between SQLite and Supabase
Performance comparison testing
Rollback and recovery testing
Large dataset migration testing

API Compatibility Testing:

Regression testing for all existing endpoints
MCP integration testing with Claude
User context switching validation
Error response format verification

7.2 User Acceptance Testing
Instructions for LLM: Design user-friendly testing procedures:
Authentication Flow Testing:

New user registration and email verification
Existing user login and session persistence
Password reset and account recovery
Multi-device session management

Migration Validation:

Data preservation verification for existing users
Performance comparison before/after migration
Feature functionality validation post-migration
Backup and restore procedure testing


🔧 Implementation Phases & Feature Requirements
Phase 1: Foundation and Schema Setup
Schema Creation and Database Setup
LLM Instructions: Create the dedicated memory_master schema infrastructure:
Schema Architecture Requirements:

Create memory_master schema in existing Supabase instance
Set appropriate schema-level permissions and ownership
Configure search path to prioritize memory_master schema
Implement schema-level backup and recovery procedures

Table Migration Specifications:

Migrate all 10 tables from existing SQLAlchemy models
Preserve all existing field types, constraints, and relationships
Maintain all existing indexes for query performance
Convert SQLite-specific features to PostgreSQL equivalents
Preserve all UUID primary keys and foreign key relationships

Security Implementation:

Enable Row Level Security on all tables within memory_master schema
Create user-specific policies for data isolation
Set up schema-level access controls for service accounts
Implement audit logging for all schema operations

Supabase Project Configuration
LLM Instructions: Configure Supabase for Memory Master integration:
Authentication Configuration:

Enable email/password authentication provider
Configure email templates with Memory Master branding
Set up custom domain for authentication if available
Configure session timeout and refresh token settings

Database Configuration:

Optimize PostgreSQL settings for memory workload
Configure connection pooling for FastAPI integration
Set up database monitoring and alerting
Create read-only replicas if high availability required

Phase 2: Authentication System Implementation
Frontend Authentication Components
LLM Instructions: Build authentication UI that seamlessly integrates with existing design:
Design System Integration:

Study existing component patterns in components/ui/ directory
Extract color scheme from tailwind.config.ts and existing CSS
Match existing typography, spacing, and layout conventions
Use existing icon library (Lucide React) for consistency

Component Development Requirements:
LoginForm Component:

Create responsive form layout matching existing form patterns
Implement email/password validation with existing error styling
Add loading states using existing spinner components
Include "Remember me" checkbox with consistent styling
Add "Forgot password" link with proper hover effects
Use existing toast notification system for errors/success

SignupForm Component:

Match LoginForm styling and validation patterns
Add password strength indicator using existing progress components
Include email verification flow with success/pending states
Add terms of service acceptance with existing checkbox styling
Implement form validation with existing error message patterns

AuthProvider Component:

Create React context for global authentication state
Handle Supabase session management and token refresh
Provide loading states during authentication operations
Manage user profile data and preferences
Handle authentication errors with existing error boundary patterns

ProtectedRoute Component:

Create higher-order component for route protection
Show loading spinner during authentication verification
Redirect to login with return URL preservation
Handle authentication state changes gracefully
Integrate with existing navigation and routing patterns

User Profile Components:

Create user profile display matching existing card/panel styling
Add profile editing functionality with existing form patterns
Include password change functionality with validation
Add account deletion with existing confirmation dialog patterns
Display user statistics and memory counts with existing metric styling

Backend Authentication Integration
LLM Instructions: Implement authentication middleware maintaining API compatibility:
JWT Validation System:

Create middleware to validate Supabase JWT tokens on each request
Extract user information from token payload securely
Handle token expiration with appropriate error responses
Implement token refresh logic for long-running sessions
Provide fallback authentication for MCP integration

User Management System:

Auto-create internal user records for new Supabase users
Map Supabase user IDs to existing user table structure
Handle user profile updates from authentication changes
Maintain existing user-app-memory relationship structure
Preserve all existing user metadata and preferences

API Endpoint Enhancement:

Add optional authentication headers to all existing endpoints
Maintain backward compatibility with unauthenticated requests
Implement user context injection for authenticated requests
Preserve existing response formats and error handling
Add user-specific data filtering for authenticated requests

Phase 3: Database Migration and Dual-Write System
Migration Infrastructure Development
LLM Instructions: Create robust migration system with comprehensive validation:
Pre-Migration System:

Create SQLite database integrity checker
Implement foreign key relationship validator
Build complete backup system with compression
Create rollback procedures and validation scripts
Implement migration readiness assessment tools

Schema Migration Tools:

Build schema generator from existing SQLAlchemy models
Create index and constraint migration utilities
Implement data type conversion logic (SQLite to PostgreSQL)
Build foreign key relationship preservation tools
Create schema validation and comparison utilities

Data Migration Engine:

Implement batched data migration with progress tracking
Create data validation and integrity checking tools
Build parallel processing for large table migrations
Implement error handling and recovery procedures
Create data comparison and verification utilities

Dual-Write Implementation:

Build dual-write coordinator for both databases
Implement conflict resolution for concurrent writes
Create synchronization validation and monitoring
Build manual sync tools for data consistency
Implement health checking for both database connections

Migration Validation and Monitoring
LLM Instructions: Implement comprehensive validation system:
Data Integrity Validation:

Compare row counts between SQLite and Supabase
Validate foreign key relationships in migrated data
Perform random sampling validation of record content
Check data type preservation and conversion accuracy
Validate UUID preservation and uniqueness

Performance Validation:

Benchmark query performance before and after migration
Compare memory usage and connection patterns
Validate index effectiveness in PostgreSQL
Test concurrent access performance improvements
Monitor resource utilization during migration

Business Logic Validation:

Test all existing API endpoints with migrated data
Validate MCP server functionality with new database
Test memory creation, retrieval, and search functionality
Validate user-app-memory relationship preservation
Test category and tagging system functionality

Phase 4: Integration and Enhancement
MCP Server Compatibility Maintenance
LLM Instructions: Ensure Claude integration remains fully functional:
Protocol Preservation:

Maintain existing MCP server endpoint structure
Preserve current request/response schemas
Keep existing error handling and status codes
Ensure backward compatibility with existing Claude configuration
Maintain existing WebSocket or HTTP communication patterns

User Context Integration:

Implement optional user identification for MCP requests
Allow MCP to operate with default user during transition
Add authenticated user context for personalized responses
Maintain existing memory retrieval and storage capabilities
Preserve existing search and categorization functionality

UI/UX Polish and Enhancement
LLM Instructions: Enhance user interface while maintaining design consistency:
Navigation Enhancement:

Add authentication status indicators to existing navigation
Include user profile access in existing header/menu structure
Show/hide menu items based on authentication state
Add logout functionality with existing styling patterns
Display user-specific memory counts and statistics

Memory Interface Enhancement:

Add user-specific memory filtering to existing interfaces
Enhance existing search functionality with user context
Maintain existing memory creation and editing workflows
Preserve existing categorization and tagging interfaces
Keep existing memory visualization and browsing patterns

Settings and Configuration UI:

Create user preferences interface matching existing settings patterns
Add account management features with existing form styling
Include data export/import functionality with existing file handling
Add privacy and security settings with existing switch/toggle styling
Maintain existing theme and display preference interfaces

Performance and Reliability Enhancements
LLM Instructions: Optimize system performance and reliability:
Database Performance Optimization:

Implement connection pooling for PostgreSQL connections
Add query optimization and index analysis tools
Create database monitoring and alerting systems
Implement caching strategies for frequently accessed data
Add database backup and recovery automation

Application Performance Enhancement:

Implement client-side caching for user data
Add lazy loading for large memory datasets
Optimize API response caching and compression
Implement progressive loading for memory content
Add offline capability for read-only operations

Monitoring and Observability:

Add authentication event logging and monitoring
Implement user activity tracking and analytics
Create system health monitoring and alerting
Add performance metrics collection and visualization
Implement error tracking and reporting systems