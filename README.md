# AY Team's Internal Memory Project

This is AY Team's internal memory project, which will be adding more features in the future. It is your personal memory layer for LLMs - private, portable, and open-source. Your memories live locally, giving you complete control over your data.

## Known Issues

### Claude <PERSON>op Text Length Limitations

**Issue**: <PERSON> has a vector store context inconsistency that affects medium and long text storage.

**Root Cause**: Each request from <PERSON> may create a new `mem0` client instance. This can lead to an inconsistent state where the vector store sometimes appears empty (0 memories), even when the database has hundreds of entries. This causes `mem0`'s internal LLM to reject content it deems "not memorable" due to the lack of context.

**Current Status**:
- ✅ **Short text (under ~60 words)**: Works reliably.
- 🔄 **Medium text (60-150 words)**: Inconsistent success. Fails if the `mem0` instance has an empty context.
- ❌ **Long text (150+ words)**: Consistently fails, as the first chunk is often rejected due to the empty context issue.

**Workaround**: For now, **use short, concise text with <PERSON>*<PERSON> to ensure memories are saved reliably.

**Long-term Solution**: The underlying issue requires a singleton `mem0` client architecture to ensure a consistent vector store context is maintained across all requests. This is a more significant architectural change planned for a future update.

**Note**: The original system-hanging bug with long text has been **completely resolved** by implementing automatic chunking. All other clients, like `roocline`, work as expected.

## Future Enhancements

The following features are planned for future releases to improve the project's functionality and address known limitations:

1.  **Intelligent Cleaning System**: Implement a robust cleaning mechanism to manage database size and performance. This will include:
    *   **Hard Deletion**: A scheduled process to permanently remove soft-deleted memories after a configurable retention period.
    *   **Archiving**: The ability to archive old or inactive memories to a separate, lower-cost storage solution, allowing for historical analysis without impacting the primary database's performance.

2.  **Database Consolidation**:
    *   **Remove Dual Storage**: Eliminate the dual storage system by completely removing the SQLite database.
    *   **Standardize on Supabase**: Fully migrate to Supabase as the single source of truth for all data, simplifying the architecture and reducing maintenance overhead.

3.  **Enhanced Claude Desktop Integration**:
    *   **Support for Long Text**: Re-architect the Claude Desktop integration to allow for the storage and retrieval of long-form text memories. This will involve implementing a singleton `mem0` client to ensure a consistent vector store context across all requests.
## `MemoryMaster` Library Limitation

When saving long memory texts—such as documents with hundreds of lines—OpenMemory currently breaks them down and only saves small memory entries, typically just a single sentence per entry, regardless of how large the input was. This causes larger texts to be split into very small chunks, resulting in a loss of context and limiting the effectiveness of memory recall. Users have reported this behavior even after increasing chunk size settings.

This isn’t necessarily a bug, but rather a result of how mem0’s memory extraction and chunking logic works. Even if chunking parameters are increased, mem0’s default behavior is to filter and store only what it determines to be the most “memorable” information, which usually equates to short, relevant snippets—often just sentences or brief facts. This mechanism is designed to avoid cluttering memory with irrelevant or overly general data, but it does create the limitation you mentioned.

If you need to store larger blocks of memory (like whole paragraphs or documents), you may need to modify mem0’s chunking and filtering logic or look out for future updates addressing this use case. Several GitHub issues have been filed and discussed on this topic, and developers have requested additional settings or code changes to improve large memory entry support.

## Recommendations for Efficient Usage

To work efficiently with mem0 (and OpenMemory) in your VS Code and Claude Desktop workflow while preserving its benefits and mitigating the "short memory chunk" issue, you’ll want to set up your client rules and usage habits as follows:

### 1. Write memory input for extraction:

When adding memories, focus on information that is:

*   Personal, project- or session-specific, not merely generic facts.
*   Anchored with temporal/context markers (“Today in project EVA...”, “On June 14, I changed...”, “During my last debugging session...”).
*   Linked to action or consequence (“I updated the eBay API handler, which fixed the error with...”, “Found Cline’s TypeScript templates the most reusable…”).
*   Concrete and detailed rather than abstract (“I prefer async/await for browser automation tasks in Playwright.” instead of “Async/await is good for automation.”).

### 2. Use metadata and tagging for retrieval:

Take advantage of mem0’s ability to store metadata like topic, timestamp, tool/client/app, or project for each memory entry.

For example: When working in VS Code or Claude Desktop, tag or provide metadata for which client was used, the project context (“#eva #vscode #browser-automation”), or even feature/bug label.

This makes targeted recall and search possible: you can then instruct your agent to “Recall EVA debugging, VS Code, last week”—retrieving the most relevant chunk without noise.

### 3. Automate consistent summary and update rules:

Structure your agent/tool so that after each significant work session (e.g., feature complete, bug fix, API integration), you generate structured summaries:

“Session summary: Added Playwright e2e. Issue: flaky login fixed with await page.waitForSelector.

This increases the likelihood mem0 will accept the whole contextual passage rather than segmenting it into tiny, less useful bits.

### 4. Lean on MemoryMaster MCP for cross-tool sharing:

Store core requirements, preferences, learned lessons, and decisions in OpenMemory with robust metadata, so VS Code, Claude, and any other MCP-compatible tool (e.g., Windsurf, Cursor) have access to the same memory—no need for repeated explanations.

For example, Claude Desktop can automatically pull relevant rules from your project’s OpenMemory context when the session starts, so its answers and code generation reflect your unique workflow and decisions.

### 5. Set client environment/config for optimal behavior:

Configure mem0 to run with telemetry off if privacy is needed.

Optionally, set storage paths and persistence as fits your dev environment—OpenMemory makes it easy to run fully local and manage access via its dashboard.

Use the dashboard to review/edit memories, delete irrelevant or outdated entries, and tune access permissions for different clients/tools as needed.

### Summary of concrete client rules and routines:

*   Always phrase new memories as personal, time-anchored, specific to your project/workflow.
*   Add or auto-attach metadata/tags per client, project, and topic.
*   Regularly summarize key sessions and save them as single memory entries.
*   Share memory across tools via `MemoryMaster` mcp, centralizing and unifying context.

Following these practices allows you to leverage mem0’s strengths (contextual, cost-efficient recall, fast search, privacy) and sidestep pitfalls like fragmented or generic memories, ensuring your LLM-powered tools remain genuinely useful and aligned with your day-to-day workflows.

## Easy Setup

### Prerequisites
- Docker
- OpenAI API Key

You can quickly run OpenMemory by running the following command:

```bash
curl -sL https://raw.githubusercontent.com/mem0ai/mem0/main/openmemory/run.sh | bash
```

You should set the `OPENAI_API_KEY` as a global environment variable:

```bash
export OPENAI_API_KEY=your_api_key
```

You can also set the `OPENAI_API_KEY` as a parameter to the script:

```bash
curl -sL https://raw.githubusercontent.com/mem0ai/mem0/main/openmemory/run.sh | OPENAI_API_KEY=your_api_key bash
```

## Prerequisites

- Docker and Docker Compose
- Python 3.9+ (for backend development)
- Node.js (for frontend development)
- OpenAI API Key (required for LLM interactions, run `cp api/.env.example api/.env` then change **OPENAI_API_KEY** to yours)

## Quickstart

### 1. Set Up Environment Variables

Before running the project, you need to configure environment variables for both the API and the UI.

You can do this in one of the following ways:

- **Manually**:  
  Create a `.env` file in each of the following directories:
  - `/api/.env`
  - `/ui/.env`

- **Using `.env.example` files**:  
  Copy and rename the example files:

  ```bash
  cp api/.env.example api/.env
  cp ui/.env.example ui/.env
  ```

 - **Using Makefile** (if supported):  
    Run:
   
   ```bash
   make env
   ```
- #### Example `/api/.env`

```env
OPENAI_API_KEY=sk-xxx
USER=<user-id> # The User Id you want to associate the memories with 
```
- #### Example `/ui/.env`

```env
NEXT_PUBLIC_API_URL=http://localhost:8765
NEXT_PUBLIC_USER_ID=<user-id> # Same as the user id for environment variable in api
```

### 2. Build and Run the Project
You can run the project using the following two commands:
```bash
make build # builds the mcp server and ui
make up  # runs openmemory mcp server and ui
```

After running these commands, you will have:
- OpenMemory MCP server running at: http://localhost:8765 (API documentation available at http://localhost:8765/docs)
- OpenMemory UI running at: http://localhost:3000

#### UI not working on `localhost:3000`?

If the UI does not start properly on [http://localhost:3000](http://localhost:3000), try running it manually:

```bash
cd ui
pnpm install
pnpm dev
```


## Project Structure

- `api/` - Backend APIs + MCP server
- `ui/` - Frontend React application
