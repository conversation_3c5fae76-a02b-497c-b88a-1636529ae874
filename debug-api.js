const axios = require('axios');

async function testApiCalls() {
  const baseURL = 'http://localhost:8765';
  const appId = 'b55cd1d3-c236-4e48-a339-b71dbee1d968';
  
  console.log('Testing API endpoints...\n');
  
  try {
    // Test 1: List all apps
    console.log('1. Testing /api/v1/apps');
    const appsResponse = await axios.get(`${baseURL}/api/v1/apps`);
    console.log('✅ Apps list:', appsResponse.data.total, 'apps found');
    
    // Test 2: Get specific app details
    console.log('\n2. Testing /api/v1/apps/' + appId);
    const appDetailsResponse = await axios.get(`${baseURL}/api/v1/apps/${appId}`);
    console.log('✅ App details:', appDetailsResponse.data);
    
    // Test 3: Get app memories
    console.log('\n3. Testing /api/v1/apps/' + appId + '/memories');
    const memoriesResponse = await axios.get(`${baseURL}/api/v1/apps/${appId}/memories`);
    console.log('✅ App memories:', memoriesResponse.data.total, 'memories found');
    
    // Test 4: Get accessed memories
    console.log('\n4. Testing /api/v1/apps/' + appId + '/accessed');
    const accessedResponse = await axios.get(`${baseURL}/api/v1/apps/${appId}/accessed`);
    console.log('✅ Accessed memories:', accessedResponse.data.total, 'memories found');
    
    console.log('\n🎉 All API endpoints are working correctly!');
    
  } catch (error) {
    console.error('❌ API Error:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    }
  }
}

testApiCalls();
