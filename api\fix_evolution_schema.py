#!/usr/bin/env python3
"""Fix evolution tables schema by adding missing app_id columns."""

from app.database import <PERSON>Local
from sqlalchemy import text
import uuid

def fix_evolution_schema():
    """Add missing app_id columns to evolution tables."""
    db = SessionLocal()
    
    try:
        print("🔧 Fixing evolution tables schema...")
        
        # Check if app_id column exists in evolution_operations
        result = db.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'evolution_operations' 
            AND table_schema = 'memory_master'
            AND column_name = 'app_id'
        """)).fetchall()
        
        if not result:
            print("Adding app_id column to evolution_operations...")
            db.execute(text("""
                ALTER TABLE memory_master.evolution_operations 
                ADD COLUMN app_id UUID
            """))
            print("✅ Added app_id column to evolution_operations")
        else:
            print("✅ app_id column already exists in evolution_operations")
        
        # Check if app_id column exists in evolution_insights
        result = db.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'evolution_insights' 
            AND table_schema = 'memory_master'
            AND column_name = 'app_id'
        """)).fetchall()
        
        if not result:
            print("Adding app_id column to evolution_insights...")
            db.execute(text("""
                ALTER TABLE memory_master.evolution_insights 
                ADD COLUMN app_id UUID
            """))
            print("✅ Added app_id column to evolution_insights")
        else:
            print("✅ app_id column already exists in evolution_insights")
        
        # Check if average_similarity column exists in evolution_insights
        result = db.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'evolution_insights' 
            AND table_schema = 'memory_master'
            AND column_name = 'average_similarity'
        """)).fetchall()
        
        if not result:
            print("Adding average_similarity column to evolution_insights...")
            db.execute(text("""
                ALTER TABLE memory_master.evolution_insights 
                ADD COLUMN average_similarity DOUBLE PRECISION
            """))
            print("✅ Added average_similarity column to evolution_insights")
        else:
            print("✅ average_similarity column already exists in evolution_insights")
        
        # Commit changes
        db.commit()
        print("✅ Schema fixes committed successfully")
        
        # Verify the changes
        print("\n🔍 Verifying schema changes...")
        
        result = db.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'evolution_operations' 
            AND table_schema = 'memory_master'
            ORDER BY column_name
        """)).fetchall()
        
        print('evolution_operations columns:', [r[0] for r in result])
        
        result = db.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'evolution_insights' 
            AND table_schema = 'memory_master'
            ORDER BY column_name
        """)).fetchall()
        
        print('evolution_insights columns:', [r[0] for r in result])
        
        return True
        
    except Exception as e:
        print(f"❌ Error fixing schema: {e}")
        db.rollback()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    success = fix_evolution_schema()
    if success:
        print("\n🎉 Evolution schema fixed successfully!")
    else:
        print("\n❌ Failed to fix evolution schema")
