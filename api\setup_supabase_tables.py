#!/usr/bin/env python3
"""
Setup Supabase PostgreSQL tables
Creates all necessary tables in the memory_master schema
"""
import os
import sys
from sqlalchemy import create_engine, text
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.database.base import Base
from app.models import (
    User, App, Config, Memory, Category, AccessControl,
    ArchivePolicy, MemoryStatusHistory, MemoryAccessLog, memory_categories
)

def setup_supabase_tables():
    """Create all tables in Supabase PostgreSQL"""
    load_dotenv()
    
    # Get PostgreSQL URL - prioritize SUPABASE_DATABASE_URL
    postgres_url = os.getenv("SUPABASE_DATABASE_URL") or os.getenv("DATABASE_URL")
    if not postgres_url:
        print("ERROR: SUPABASE_DATABASE_URL or DATABASE_URL not found in environment")
        return False
    
    print(f"Connecting to PostgreSQL: {postgres_url}")
    
    try:
        # Create engine for PostgreSQL
        engine = create_engine(postgres_url)
        
        # Test connection
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        print("✅ PostgreSQL connection successful")
        
        # Create all tables
        print("Creating tables in memory_master schema...")
        Base.metadata.create_all(bind=engine)
        print("✅ All tables created successfully")
        
        # Verify tables were created
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'memory_master'
                ORDER BY table_name
            """))
            tables = [row[0] for row in result]
            
        print(f"✅ Created {len(tables)} tables:")
        for table in tables:
            print(f"  - {table}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error setting up tables: {e}")
        return False

if __name__ == "__main__":
    success = setup_supabase_tables()
    sys.exit(0 if success else 1)