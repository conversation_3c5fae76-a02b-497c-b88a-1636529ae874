#!/usr/bin/env python3
"""
Update Evolution Intelligence Configuration

This script updates the database configuration to include custom prompts
for mem0's evolution intelligence system.
"""
import os
import sys
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.database import SessionLocal
from app.models import Config as ConfigModel
from app.utils.evolution_prompts import get_default_technical_prompts

def update_evolution_config():
    """Update the database configuration with evolution intelligence prompts."""
    load_dotenv()
    
    try:
        db = SessionLocal()
        
        # Get existing configuration
        config_record = db.query(ConfigModel).filter(ConfigModel.key == "main").first()
        
        if not config_record:
            print("No main configuration found in database. Creating new configuration...")
            # Create default configuration
            from app.routers.config import get_default_configuration
            config_value = get_default_configuration()
            config_record = ConfigModel(key="main", value=config_value)
            db.add(config_record)
        else:
            config_value = config_record.value
        
        # Get technical prompts
        technical_prompts = get_default_technical_prompts()
        
        # Ensure mem0 section exists
        if "mem0" not in config_value:
            config_value["mem0"] = {}
        
        # Update mem0 configuration with evolution intelligence
        config_value["mem0"]["version"] = "v1.1"
        config_value["mem0"]["custom_fact_extraction_prompt"] = technical_prompts["custom_fact_extraction_prompt"]
        config_value["mem0"]["custom_update_memory_prompt"] = technical_prompts["custom_update_memory_prompt"]
        
        # Update the configuration record
        config_record.value = config_value
        
        # Commit changes
        db.commit()
        db.refresh(config_record)
        
        print("✅ Evolution intelligence configuration updated successfully")
        print(f"✅ Custom fact extraction prompt: {len(technical_prompts['custom_fact_extraction_prompt'])} characters")
        print(f"✅ Custom update memory prompt: {len(technical_prompts['custom_update_memory_prompt'])} characters")
        print("✅ mem0 version set to v1.1 for custom prompt support")
        
        return True
        
    except Exception as e:
        print(f"❌ Error updating evolution configuration: {e}")
        return False
    finally:
        db.close()

if __name__ == "__main__":
    success = update_evolution_config()
    sys.exit(0 if success else 1)
