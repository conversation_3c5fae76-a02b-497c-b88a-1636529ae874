["api/tests/test_evolution_basic.py::TestEvolutionBasic::test_database_schema_validation", "api/tests/test_evolution_basic.py::TestEvolutionBasic::test_evolution_insight_creation", "api/tests/test_evolution_basic.py::TestEvolutionBasic::test_evolution_operation_creation", "api/tests/test_evolution_integration.py::TestEvolutionIntegration::test_concurrent_evolution_operations", "api/tests/test_evolution_integration.py::TestEvolutionIntegration::test_end_to_end_evolution_workflow", "api/tests/test_evolution_integration.py::TestEvolutionIntegration::test_evolution_data_consistency", "api/tests/test_evolution_integration.py::TestEvolutionIntegration::test_evolution_metrics_aggregation", "api/tests/test_evolution_integration.py::TestEvolutionIntegration::test_evolution_performance_requirements", "api/tests/test_evolution_integration.py::TestEvolutionIntegration::test_evolution_success_criteria_validation", "api/tests/test_evolution_integration.py::TestEvolutionIntegration::test_evolution_system_reliability", "api/tests/test_evolution_integration.py::TestEvolutionIntegration::test_evolution_transaction_rollback", "api/tests/test_evolution_integration.py::TestEvolutionIntegration::test_mcp_evolution_tools_integration", "api/tests/test_evolution_integration.py::TestEvolutionIntegration::test_multi_chunk_evolution_transaction", "api/tests/test_evolution_intelligence.py::TestEvolutionIntelligenceCore::test_cascade_delete_behavior", "api/tests/test_evolution_intelligence.py::TestEvolutionIntelligenceCore::test_evolution_insight_creation", "api/tests/test_evolution_intelligence.py::TestEvolutionIntelligenceCore::test_evolution_insight_indexes", "api/tests/test_evolution_intelligence.py::TestEvolutionIntelligenceCore::test_evolution_insight_unique_constraint", "api/tests/test_evolution_intelligence.py::TestEvolutionIntelligenceCore::test_evolution_operation_creation", "api/tests/test_evolution_intelligence.py::TestEvolutionIntelligenceCore::test_evolution_operation_indexes", "api/tests/test_evolution_intelligence.py::TestEvolutionIntelligenceCore::test_evolution_operation_types", "api/tests/test_evolution_intelligence.py::TestEvolutionIntelligenceCore::test_foreign_key_constraints", "api/tests/test_evolution_intelligence.py::TestEvolutionIntelligenceCore::test_memory_set_null_behavior"]