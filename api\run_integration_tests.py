#!/usr/bin/env python3
"""
Comprehensive integration test runner for the memory system.

This script runs the complete integration test suite with proper reporting
and categorization of test results.
"""

import sys
import os
import argparse
import subprocess
import time
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def run_command(cmd, description=""):
    """Run a command and return the result."""
    print(f"\n{'='*60}")
    print(f"Running: {description or cmd}")
    print(f"{'='*60}")
    
    start_time = time.time()
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    duration = time.time() - start_time
    
    print(f"Duration: {duration:.2f}s")
    print(f"Exit code: {result.returncode}")
    
    if result.stdout:
        print(f"\nSTDOUT:\n{result.stdout}")
    
    if result.stderr:
        print(f"\nSTDERR:\n{result.stderr}")
    
    return result


def run_test_category(category, test_files, markers=None, extra_args=""):
    """Run a specific category of tests."""
    print(f"\n{'#'*80}")
    print(f"# {category.upper()} TESTS")
    print(f"{'#'*80}")
    
    if not test_files:
        print(f"No test files specified for {category}")
        return True
    
    # Build pytest command
    cmd_parts = ["python", "-m", "pytest", "-v"]
    
    if markers:
        for marker in markers:
            cmd_parts.extend(["-m", marker])
    
    # Add test files
    cmd_parts.extend(test_files)
    
    # Add extra arguments
    if extra_args:
        cmd_parts.extend(extra_args.split())
    
    cmd = " ".join(cmd_parts)
    result = run_command(cmd, f"{category} Tests")
    
    return result.returncode == 0


def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(description="Run comprehensive integration tests")
    parser.add_argument("--category", choices=["all", "integration", "reliability", "performance", "api"], 
                       default="all", help="Test category to run")
    parser.add_argument("--fast", action="store_true", help="Skip slow tests")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--parallel", "-n", type=int, help="Number of parallel workers")
    parser.add_argument("--coverage", action="store_true", help="Generate coverage report")
    parser.add_argument("--html-report", action="store_true", help="Generate HTML test report")
    parser.add_argument("--junit-xml", help="Generate JUnit XML report")
    
    args = parser.parse_args()
    
    # Ensure we're in the right directory
    os.chdir(Path(__file__).parent)
    
    print("Memory System - Comprehensive Integration Test Suite")
    print("=" * 60)
    print(f"Test category: {args.category}")
    print(f"Fast mode: {args.fast}")
    print(f"Verbose: {args.verbose}")
    if args.parallel:
        print(f"Parallel workers: {args.parallel}")
    
    # Define test categories
    test_categories = {
        "integration": {
            "files": ["tests/test_comprehensive_integration.py"],
            "markers": ["integration"],
            "description": "Core integration tests"
        },
        "reliability": {
            "files": ["tests/test_reliability_scenarios.py"],
            "markers": ["reliability"],
            "description": "Reliability and failure scenario tests"
        },
        "performance": {
            "files": ["tests/test_performance_concurrent.py"],
            "markers": ["concurrent", "slow"] if not args.fast else ["concurrent"],
            "description": "Performance and concurrency tests"
        },
        "api": {
            "files": ["tests/test_end_to_end_scenarios.py"],
            "markers": [],
            "description": "End-to-end API tests"
        }
    }
    
    # Build pytest arguments
    extra_args = []
    
    if args.verbose:
        extra_args.append("-v")
    
    if args.fast:
        extra_args.extend(["-m", "not slow"])
    
    if args.parallel:
        extra_args.extend(["-n", str(args.parallel)])
    
    if args.coverage:
        extra_args.extend(["--cov=app", "--cov-report=term-missing"])
        if args.html_report:
            extra_args.append("--cov-report=html")
    
    if args.junit_xml:
        extra_args.extend(["--junit-xml", args.junit_xml])
    
    if args.html_report and not args.coverage:
        extra_args.extend(["--html=test_report.html", "--self-contained-html"])
    
    extra_args_str = " ".join(extra_args)
    
    # Run tests based on category
    success = True
    
    if args.category == "all":
        # Run all test categories
        for category_name, category_info in test_categories.items():
            if not args.fast or category_name != "performance":
                category_success = run_test_category(
                    category_info["description"],
                    category_info["files"],
                    category_info["markers"],
                    extra_args_str
                )
                success = success and category_success
    else:
        # Run specific category
        if args.category in test_categories:
            category_info = test_categories[args.category]
            success = run_test_category(
                category_info["description"],
                category_info["files"],
                category_info["markers"],
                extra_args_str
            )
        else:
            print(f"Unknown test category: {args.category}")
            return 1
    
    # Summary
    print(f"\n{'#'*80}")
    print("# TEST SUMMARY")
    print(f"{'#'*80}")
    
    if success:
        print("✅ All tests passed!")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1


def check_dependencies():
    """Check if required dependencies are available."""
    print("Checking test dependencies...")
    
    required_packages = [
        "pytest",
        "pytest-cov",
        "pytest-html",
        "pytest-xdist",
        "psutil"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"Missing required packages: {', '.join(missing_packages)}")
        print("Install them with: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ All dependencies available")
    return True


def setup_test_environment():
    """Set up the test environment."""
    print("Setting up test environment...")
    
    # Set environment variables for testing
    os.environ["TESTING"] = "true"
    os.environ["LOG_LEVEL"] = "INFO"
    
    # Create test directories if they don't exist
    test_dirs = ["tests/reports", "tests/coverage"]
    for test_dir in test_dirs:
        Path(test_dir).mkdir(parents=True, exist_ok=True)
    
    print("✅ Test environment ready")


if __name__ == "__main__":
    # Check dependencies first
    if not check_dependencies():
        sys.exit(1)
    
    # Set up test environment
    setup_test_environment()
    
    # Run tests
    exit_code = main()
    sys.exit(exit_code)
