# Memory Master Deployment Guidelines

## Overview
Memory Master is a sophisticated memory management system providing AI-enhanced memory storage and retrieval via both REST API and MCP protocol integration. This guide covers deployment, configuration, monitoring, and troubleshooting.

## Prerequisites
- Docker and Docker Compose
- OpenAI API key
- Qdrant vector database (included in docker-compose)
- Network access to required services

## Deployment Steps

### 1. Environment Configuration
Create `.env` file in project root:
```bash
OPENAI_API_KEY=your_openai_api_key
QDRANT_URL=http://mem0_store:6333
USER_ID=aungheinaye  # Configure as needed
```

### 2. Container Deployment
```bash
# Start all services
docker-compose up -d

# Verify containers are running
docker ps | grep memory

# Check service health
curl http://localhost:8765/health
```

### 3. Service Verification
```bash
# Test memory creation
curl -X POST "http://localhost:8765/api/v1/memories/" \
  -H "Content-Type: application/json" \
  -d '{"text": "Deployment test memory", "app": "deployment_test"}'

# Verify MCP endpoint
curl http://localhost:8765/mcp
```

## Configuration

### Core Settings
- **API Port**: 8765 (configurable)
- **Vector Store**: Qdrant on port 6333
- **Max Text Length**: 2000 characters
- **Health Check Interval**: 300 seconds
- **Request Timeout**: 30 seconds

### Memory System Features
- **Automatic Chunking**: For texts exceeding limits
- **Degraded Mode**: Database fallback during vector store issues
- **Multi-Protocol**: REST API + MCP protocol support
- **Real-time Health Monitoring**: Automatic service recovery

## Monitoring & Health Checks

### Key Endpoints
- **Health**: `GET /health` - Service status and dependencies
- **Stats**: `GET /api/v1/stats` - System metrics and performance
- **Memories**: `GET /api/v1/memories` - Memory management

### Performance Expectations
| Operation | Normal Mode | Degraded Mode |
|-----------|-------------|---------------|
| Memory Creation | < 2 seconds | < 1 second |
| Chunked Operations | < 5 seconds | < 2 seconds |
| Health Checks | < 1 second | < 500ms |
| Search Queries | < 500ms | < 200ms |

### Monitoring Setup
```bash
# Health monitoring script
while true; do
  curl -s http://localhost:8765/health | jq '.status'
  sleep 30
done

# Performance monitoring
curl -s http://localhost:8765/api/v1/stats | jq '.'
```

## Error Handling & Troubleshooting

### Expected Behaviours
- **422 Responses**: Normal when content is filtered as non-memorable
- **Degraded Mode**: Automatic vector store fallback
- **Empty Results**: Expected for duplicate or trivial content
- **Auto Recovery**: Service attempts reconnection every 5 minutes

### Alert Conditions
| Issue | Severity | Action Required |
|-------|----------|----------------|
| 500 Errors | Critical | Immediate investigation |
| Persistent Degraded Mode | High | Check vector store connectivity |
| High Response Times | Medium | Monitor system resources |
| KeyError Exceptions | Critical | Contact development team |

### Common Issues & Solutions

1. **Memory Not Storing**
   - Verify content is meaningful (system filters trivial content)
   - Check OpenAI API key configuration
   - Confirm vector store connectivity

2. **Slow Performance**
   - Monitor OpenAI API rate limits
   - Check Qdrant container resources
   - Verify network latency

3. **Service Connectivity**
   - Check container status: `docker ps`
   - Review logs: `docker logs memory-mcp --tail=50`
   - Test endpoints: `curl -v http://localhost:8765/health`

### Debug Commands
```bash
# Container status
docker-compose ps

# Service logs
docker-compose logs -f memory-mcp

# Vector store status
curl http://localhost:6333/collections

# Database connectivity
docker exec -it memory-mcp python -c "from app.database import get_db; print('DB Connected')"
```

## Security Considerations
- API keys stored in environment variables only
- No sensitive data in logs
- Container isolation enabled
- Network access restricted to required ports

## Backup & Recovery
- Database: Automatic container volume persistence
- Vector Store: Qdrant handles internal persistence
- Configuration: Version controlled environment files
- Recovery: Container restart restores full functionality

## Claude Desktop Integration
For MCP protocol integration:
1. Configure Claude Desktop to point to `http://localhost:8765/mcp`
2. Verify session handling for multiple users
3. Test memory persistence across sessions

## Go-Live Checklist
- [ ] Environment variables configured
- [ ] All containers running and healthy
- [ ] API endpoints responding correctly
- [ ] Vector store operational
- [ ] Monitoring configured
- [ ] Backup procedures verified
- [ ] Team notified

## Support & Escalation
1. **First Level**: Check container logs and health endpoints
2. **Second Level**: Verify external service connectivity
3. **Third Level**: Review system resources and performance
4. **Escalation**: Contact development team with specific error details

---
**System Status**: Production Ready  
**Last Updated**: December 2024  
**Verified**: Automated testing and manual verification completed 