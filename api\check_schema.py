#!/usr/bin/env python3
"""Check database schema for evolution tables."""

from app.database import SessionLocal
from sqlalchemy import text

def check_schema():
    db = SessionLocal()

    # Check evolution_operations columns
    result = db.execute(text("""
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'evolution_operations'
        AND table_schema = 'memory_master'
    """)).fetchall()

    print('evolution_operations columns:', [r[0] for r in result])

    # Check evolution_insights columns
    result = db.execute(text("""
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'evolution_insights'
        AND table_schema = 'memory_master'
    """)).fetchall()

    print('evolution_insights columns:', [r[0] for r in result])

    db.close()

if __name__ == "__main__":
    check_schema()
