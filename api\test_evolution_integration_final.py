#!/usr/bin/env python3
"""
Final Evolution Intelligence Integration Test

This script performs a comprehensive integration test of the evolution intelligence
features to validate that all components work together correctly.
"""

import os
import sys
import time
import uuid
from datetime import datetime, timezone

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from dotenv import load_dotenv
load_dotenv()

def test_database_connection():
    """Test database connection and schema."""
    print("🔍 Testing database connection...")
    
    try:
        from app.database import SessionLocal
        from app.models import EvolutionOperation, EvolutionInsight, Config
        
        db = SessionLocal()
        
        # Test basic connection
        from sqlalchemy import text
        result = db.execute(text("SELECT 1")).fetchone()
        assert result[0] == 1
        print("✅ Database connection successful")
        
        # Test evolution tables exist
        db.query(EvolutionOperation).count()
        print("✅ evolution_operations table accessible")
        
        db.query(EvolutionInsight).count()
        print("✅ evolution_insights table accessible")
        
        # Test configuration table
        config_count = db.query(Config).count()
        print(f"✅ Configuration table accessible ({config_count} configs)")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def test_custom_prompts_configuration():
    """Test custom prompts are loaded in configuration."""
    print("\n🔍 Testing custom prompts configuration...")
    
    try:
        from app.utils.memory import get_default_memory_config
        from app.utils.evolution_prompts import get_default_technical_prompts
        
        # Test default config includes prompts
        config = get_default_memory_config()
        assert "custom_fact_extraction_prompt" in config
        assert "custom_update_memory_prompt" in config
        assert config["version"] == "v1.1"
        print("✅ Default memory config includes custom prompts")
        
        # Test prompts are not empty
        prompts = get_default_technical_prompts()
        assert len(prompts["custom_fact_extraction_prompt"]) > 1000
        assert len(prompts["custom_update_memory_prompt"]) > 1000
        print("✅ Technical prompts loaded and non-empty")
        
        # Test prompts contain expected content
        fact_prompt = prompts["custom_fact_extraction_prompt"]
        assert "technical" in fact_prompt.lower()
        assert "programming" in fact_prompt.lower()
        assert "json" in fact_prompt.lower()
        print("✅ Fact extraction prompt contains expected technical content")
        
        update_prompt = prompts["custom_update_memory_prompt"]
        assert "ADD" in update_prompt and "UPDATE" in update_prompt
        assert "DELETE" in update_prompt and "NOOP" in update_prompt
        print("✅ Update memory prompt contains expected operations")
        
        return True
        
    except Exception as e:
        print(f"❌ Custom prompts test failed: {e}")
        return False

def test_evolution_service():
    """Test evolution service functionality."""
    print("\n🔍 Testing evolution service...")
    
    try:
        from app.services.evolution_service import evolution_service
        
        # Test service methods exist
        assert hasattr(evolution_service, 'extract_evolution_stats')
        assert hasattr(evolution_service, 'store_evolution_operation')
        assert hasattr(evolution_service, 'get_evolution_metrics')
        assert hasattr(evolution_service, 'get_learning_insights')
        assert hasattr(evolution_service, 'get_evolution_monitor')
        print("✅ Evolution service has all required methods")
        
        # Test metrics methods return strings
        user_id = str(uuid.uuid4())
        
        metrics = evolution_service.get_evolution_metrics(user_id, "week")
        assert isinstance(metrics, str)
        assert "Evolution Intelligence Metrics" in metrics
        print("✅ get_evolution_metrics returns formatted response")
        
        insights = evolution_service.get_learning_insights(user_id)
        assert isinstance(insights, str)
        assert "Learning Insights" in insights
        print("✅ get_learning_insights returns formatted response")
        
        monitor = evolution_service.get_evolution_monitor(user_id, limit=5)
        assert isinstance(monitor, str)
        assert "Recent Evolution Activity" in monitor
        print("✅ get_evolution_monitor returns formatted response")
        
        return True
        
    except Exception as e:
        print(f"❌ Evolution service test failed: {e}")
        return False

def test_memory_service_integration():
    """Test memory service integration with evolution tracking."""
    print("\n🔍 Testing memory service integration...")
    
    try:
        from app.memory_service import MemoryService
        
        memory_service = MemoryService()
        
        # Test that memory service has evolution tracking method
        assert hasattr(memory_service, '_track_evolution_operations')
        print("✅ Memory service has evolution tracking method")
        
        # Test method can be called without errors (with mock data)
        class MockResponse:
            def __init__(self):
                self.results = None
                self.memories = None
        
        mock_response = MockResponse()
        user_id = str(uuid.uuid4())
        app_id = "test_app"
        
        # This should not raise an exception
        memory_service._track_evolution_operations(mock_response, user_id, app_id, "test text")
        print("✅ Evolution tracking method executes without errors")
        
        return True
        
    except Exception as e:
        print(f"❌ Memory service integration test failed: {e}")
        return False

def test_mcp_tools():
    """Test MCP tools are properly defined."""
    print("\n🔍 Testing MCP tools...")
    
    try:
        from app.mcp_server import get_evolution_metrics, get_learning_insights, get_evolution_monitor
        
        # Test tools are callable
        assert callable(get_evolution_metrics)
        assert callable(get_learning_insights)
        assert callable(get_evolution_monitor)
        print("✅ All evolution MCP tools are callable")
        
        # Test tools have proper docstrings
        assert get_evolution_metrics.__doc__ is not None
        assert get_learning_insights.__doc__ is not None
        assert get_evolution_monitor.__doc__ is not None
        print("✅ All evolution MCP tools have documentation")
        
        return True
        
    except Exception as e:
        print(f"❌ MCP tools test failed: {e}")
        return False

def test_performance_requirements():
    """Test performance requirements are met."""
    print("\n🔍 Testing performance requirements...")
    
    try:
        from app.services.evolution_service import evolution_service
        
        user_id = str(uuid.uuid4())
        
        # Test evolution metrics query performance (<200ms)
        start_time = time.time()
        metrics = evolution_service.get_evolution_metrics(user_id, "week")
        metrics_time = (time.time() - start_time) * 1000
        
        assert metrics_time < 200, f"Evolution metrics took {metrics_time:.1f}ms (should be <200ms)"
        print(f"✅ Evolution metrics query: {metrics_time:.1f}ms (<200ms requirement)")
        
        # Test learning insights query performance (<200ms)
        start_time = time.time()
        insights = evolution_service.get_learning_insights(user_id)
        insights_time = (time.time() - start_time) * 1000
        
        assert insights_time < 200, f"Learning insights took {insights_time:.1f}ms (should be <200ms)"
        print(f"✅ Learning insights query: {insights_time:.1f}ms (<200ms requirement)")
        
        # Test evolution monitor query performance (<200ms)
        start_time = time.time()
        monitor = evolution_service.get_evolution_monitor(user_id, limit=10)
        monitor_time = (time.time() - start_time) * 1000
        
        assert monitor_time < 200, f"Evolution monitor took {monitor_time:.1f}ms (should be <200ms)"
        print(f"✅ Evolution monitor query: {monitor_time:.1f}ms (<200ms requirement)")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        return False

def main():
    """Run all integration tests."""
    print("🚀 Starting Evolution Intelligence Integration Tests")
    print("=" * 60)
    
    tests = [
        test_database_connection,
        test_custom_prompts_configuration,
        test_evolution_service,
        test_memory_service_integration,
        test_mcp_tools,
        test_performance_requirements
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 ALL TESTS PASSED! Evolution Intelligence integration is successful!")
        print("\n✅ Ready for production deployment")
        return True
    else:
        print(f"❌ {failed} tests failed. Please review and fix issues.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
