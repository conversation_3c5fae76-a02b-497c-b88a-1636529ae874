-- Create the dedicated schema for Memory Master v2
CREATE SCHEMA memory_master;

-- Set the search path to include the new schema
SET search_path TO memory_master, public;

-- Create the custom ENUM type for MemoryState
CREATE TYPE memory_master.memory_state AS ENUM ('active', 'paused', 'archived', 'deleted');

-- Create the users table with Supabase integration fields
CREATE TABLE memory_master.users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id TEXT NOT NULL UNIQUE,
    name TEXT,
    email TEXT UNIQUE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    supabase_user_id UUID UNIQUE,
    email_verified BOOLEAN DEFAULT FALSE,
    last_sign_in_at TIMESTAMPTZ
);
CREATE INDEX idx_users_user_id ON memory_master.users(user_id);
CREATE INDEX idx_users_name ON memory_master.users(name);
CREATE INDEX idx_users_email ON memory_master.users(email);
CREATE INDEX idx_users_created_at ON memory_master.users(created_at);

-- Create the apps table
CREATE TABLE memory_master.apps (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    owner_id UUID NOT NULL REFERENCES memory_master.users(id),
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    metadata JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);
CREATE INDEX idx_apps_owner_id ON memory_master.apps(owner_id);
CREATE INDEX idx_apps_name ON memory_master.apps(name);
CREATE INDEX idx_apps_is_active ON memory_master.apps(is_active);
CREATE INDEX idx_apps_created_at ON memory_master.apps(created_at);

-- Create the configs table
CREATE TABLE memory_master.configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    key TEXT NOT NULL UNIQUE,
    value JSONB NOT NULL,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);
CREATE INDEX idx_configs_key ON memory_master.configs(key);

-- Create the memories table
CREATE TABLE memory_master.memories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES memory_master.users(id),
    app_id UUID NOT NULL REFERENCES memory_master.apps(id),
    content TEXT NOT NULL,
    vector TEXT,
    metadata JSONB DEFAULT '{}',
    state memory_master.memory_state DEFAULT 'active',
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    archived_at TIMESTAMPTZ,
    deleted_at TIMESTAMPTZ
);
CREATE INDEX idx_memories_user_id ON memory_master.memories(user_id);
CREATE INDEX idx_memories_app_id ON memory_master.memories(app_id);
CREATE INDEX idx_memories_state ON memory_master.memories(state);
CREATE INDEX idx_memories_created_at ON memory_master.memories(created_at);
CREATE INDEX idx_memories_archived_at ON memory_master.memories(archived_at);
CREATE INDEX idx_memories_deleted_at ON memory_master.memories(deleted_at);
CREATE INDEX idx_memory_user_state ON memory_master.memories(user_id, state);
CREATE INDEX idx_memory_app_state ON memory_master.memories(app_id, state);
CREATE INDEX idx_memory_user_app ON memory_master.memories(user_id, app_id);

-- Create the categories table
CREATE TABLE memory_master.categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);
CREATE INDEX idx_categories_name ON memory_master.categories(name);
CREATE INDEX idx_categories_created_at ON memory_master.categories(created_at);

-- Create the memory_categories junction table
CREATE TABLE memory_master.memory_categories (
    memory_id UUID NOT NULL REFERENCES memory_master.memories(id),
    category_id UUID NOT NULL REFERENCES memory_master.categories(id),
    PRIMARY KEY (memory_id, category_id)
);
CREATE INDEX idx_memory_categories_memory_id ON memory_master.memory_categories(memory_id);
CREATE INDEX idx_memory_categories_category_id ON memory_master.memory_categories(category_id);
CREATE INDEX idx_memory_category ON memory_master.memory_categories(memory_id, category_id);

-- Create the access_controls table
CREATE TABLE memory_master.access_controls (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    subject_type TEXT NOT NULL,
    subject_id UUID,
    object_type TEXT NOT NULL,
    object_id UUID,
    effect TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT now()
);
CREATE INDEX idx_access_controls_subject_type ON memory_master.access_controls(subject_type);
CREATE INDEX idx_access_controls_subject_id ON memory_master.access_controls(subject_id);
CREATE INDEX idx_access_controls_object_type ON memory_master.access_controls(object_type);
CREATE INDEX idx_access_controls_object_id ON memory_master.access_controls(object_id);
CREATE INDEX idx_access_controls_effect ON memory_master.access_controls(effect);
CREATE INDEX idx_access_controls_created_at ON memory_master.access_controls(created_at);
CREATE INDEX idx_access_subject ON memory_master.access_controls(subject_type, subject_id);
CREATE INDEX idx_access_object ON memory_master.access_controls(object_type, object_id);

-- Create the archive_policies table
CREATE TABLE memory_master.archive_policies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    criteria_type TEXT NOT NULL,
    criteria_id UUID,
    days_to_archive INTEGER NOT NULL,
    created_at TIMESTAMPTZ DEFAULT now()
);
CREATE INDEX idx_archive_policies_criteria_type ON memory_master.archive_policies(criteria_type);
CREATE INDEX idx_archive_policies_criteria_id ON memory_master.archive_policies(criteria_id);
CREATE INDEX idx_archive_policies_created_at ON memory_master.archive_policies(created_at);
CREATE INDEX idx_policy_criteria ON memory_master.archive_policies(criteria_type, criteria_id);

-- Create the memory_status_history table
CREATE TABLE memory_master.memory_status_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    memory_id UUID NOT NULL REFERENCES memory_master.memories(id),
    changed_by UUID NOT NULL REFERENCES memory_master.users(id),
    old_state memory_master.memory_state NOT NULL,
    new_state memory_master.memory_state NOT NULL,
    changed_at TIMESTAMPTZ DEFAULT now()
);
CREATE INDEX idx_memory_status_history_memory_id ON memory_master.memory_status_history(memory_id);
CREATE INDEX idx_memory_status_history_changed_by ON memory_master.memory_status_history(changed_by);
CREATE INDEX idx_memory_status_history_old_state ON memory_master.memory_status_history(old_state);
CREATE INDEX idx_memory_status_history_new_state ON memory_master.memory_status_history(new_state);
CREATE INDEX idx_memory_status_history_changed_at ON memory_master.memory_status_history(changed_at);
CREATE INDEX idx_history_memory_state ON memory_master.memory_status_history(memory_id, new_state);
CREATE INDEX idx_history_user_time ON memory_master.memory_status_history(changed_by, changed_at);

-- Create the memory_access_logs table
CREATE TABLE memory_master.memory_access_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    memory_id UUID NOT NULL REFERENCES memory_master.memories(id),
    app_id UUID NOT NULL REFERENCES memory_master.apps(id),
    accessed_at TIMESTAMPTZ DEFAULT now(),
    access_type TEXT NOT NULL,
    metadata JSONB DEFAULT '{}'
);
CREATE INDEX idx_memory_access_logs_memory_id ON memory_master.memory_access_logs(memory_id);
CREATE INDEX idx_memory_access_logs_app_id ON memory_master.memory_access_logs(app_id);
CREATE INDEX idx_memory_access_logs_accessed_at ON memory_master.memory_access_logs(accessed_at);
CREATE INDEX idx_memory_access_logs_access_type ON memory_master.memory_access_logs(access_type);
CREATE INDEX idx_access_memory_time ON memory_master.memory_access_logs(memory_id, accessed_at);
CREATE INDEX idx_access_app_time ON memory_master.memory_access_logs(app_id, accessed_at);