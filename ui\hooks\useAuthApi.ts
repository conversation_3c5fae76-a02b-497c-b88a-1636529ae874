import { useState, useCallback } from 'react'
import axios from 'axios'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

interface AuthStatus {
  auth_enabled: boolean
  is_authenticated: boolean
  user: {
    user_id: string
    email: string
    name?: string
    email_verified: boolean
    is_authenticated: boolean
    supabase_user_id?: string
    created_at?: string
    last_sign_in_at?: string
  } | null
}

interface UserProfile {
  user_id: string
  email: string
  name?: string
  email_verified: boolean
  is_authenticated: boolean
  supabase_user_id?: string
  created_at?: string
  last_sign_in_at?: string
}

interface UpdateProfileRequest {
  name?: string
}

export function useAuthApi() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createAuthHeaders = useCallback(() => {
    const token = localStorage.getItem('supabase.auth.token')
    return token ? { Authorization: `Bearer ${token}` } : {}
  }, [])

  const fetchAuthStatus = useCallback(async (): Promise<AuthStatus> => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await axios.get<AuthStatus>(`${API_BASE_URL}/auth/status`, {
        headers: createAuthHeaders()
      })
      return response.data
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch auth status'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }, [createAuthHeaders])

  const fetchUserProfile = useCallback(async (): Promise<UserProfile> => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await axios.get<UserProfile>(`${API_BASE_URL}/auth/profile`, {
        headers: createAuthHeaders()
      })
      return response.data
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch user profile'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }, [createAuthHeaders])

  const updateUserProfile = useCallback(async (data: UpdateProfileRequest): Promise<UserProfile> => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await axios.put<UserProfile>(`${API_BASE_URL}/auth/profile`, data, {
        headers: {
          ...createAuthHeaders(),
          'Content-Type': 'application/json'
        }
      })
      return response.data
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update user profile'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }, [createAuthHeaders])

  return {
    loading,
    error,
    fetchAuthStatus,
    fetchUserProfile,
    updateUserProfile
  }
}