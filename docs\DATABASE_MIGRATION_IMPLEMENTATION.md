# Database Migration System Implementation

## Overview

This document describes the implementation of the database migration system for seamless transition from SQLite to Supabase PostgreSQL. The system provides dual-write functionality, data validation, and comprehensive migration tools.

## Architecture

### Core Components

1. **Migration Engine** (`api/app/database/migration.py`)
   - Handles data migration from SQLite to PostgreSQL
   - Batch processing for large datasets
   - Data validation and integrity checking
   - Progress tracking and error handling
   - Rollback procedures

2. **Dual-Write System** (`api/app/database/dual_write.py`)
   - Database abstraction layer supporting both SQLite and PostgreSQL
   - Configurable write modes: `sqlite_only`, `dual_write`, `supabase_only`
   - Write coordination with conflict resolution
   - Health checking for both database connections

3. **Database Service Layer** (`api/app/database/service.py`)
   - High-level database operations with dual-write support
   - Unified API for all database operations
   - Automatic error handling and retry logic

4. **CLI Tools** (`api/app/database/cli.py`)
   - Command-line interface for migration management
   - Status monitoring and validation tools
   - Manual sync capabilities

5. **API Endpoints** (`api/app/routers/migration.py`)
   - REST API for migration control
   - Background task management
   - Real-time status reporting

## Migration Modes

### 1. SQLite Only (`sqlite_only`)
- **Default mode** for existing installations
- All operations use SQLite database
- No PostgreSQL connection required
- Backward compatible with existing code

### 2. Dual Write (`dual_write`)
- **Transition mode** during migration
- Writes to both SQLite (primary) and PostgreSQL (secondary)
- SQLite remains the source of truth
- Enables gradual migration with fallback

### 3. Supabase Only (`supabase_only`)
- **Final mode** after successful migration
- All operations use PostgreSQL
- SQLite database can be archived
- Full Supabase feature utilization

## Environment Configuration

Add these variables to your `.env` file:

```bash
# Migration Configuration
MIGRATION_MODE=sqlite_only                    # sqlite_only | dual_write | supabase_only
MIGRATION_BATCH_SIZE=100                      # Batch size for migration operations
ENABLE_MIGRATION_LOGGING=true                # Detailed migration logging
MIGRATION_VALIDATION_ENABLED=true            # Data validation toggle
SUPABASE_DATABASE_URL=postgresql://...        # PostgreSQL connection string
```

## Migration Process

### Phase 1: Preparation

1. **Backup SQLite Database**
   ```bash
   cd api
   python migrate.py backup
   ```

2. **Configure Environment**
   ```bash
   # Set PostgreSQL connection
   export SUPABASE_DATABASE_URL="postgresql://postgres:[password]@[host]:[port]/postgres"
   
   # Enable dual-write mode
   export MIGRATION_MODE="dual_write"
   ```

3. **Verify Connections**
   ```bash
   python migrate.py health
   ```

### Phase 2: Migration Execution

1. **Start Migration**
   ```bash
   python migrate.py migrate
   ```

2. **Monitor Progress**
   ```bash
   python migrate.py status
   ```

3. **Validate Data**
   ```bash
   python migrate.py validate
   ```

### Phase 3: Transition

1. **Switch to Supabase Only**
   ```bash
   python migrate.py set-mode supabase_only
   ```

2. **Final Validation**
   ```bash
   python migrate.py validate
   ```

## CLI Commands

### Migration Commands

```bash
# Run full migration
python migrate.py migrate [--dry-run]

# Check migration status
python migrate.py status

# Validate data integrity
python migrate.py validate

# Rollback migration
python migrate.py rollback [--confirm]

# Create SQLite backup
python migrate.py backup
```

### Configuration Commands

```bash
# Set migration mode
python migrate.py set-mode <mode>

# Check database health
python migrate.py health

# Get current configuration
python migrate.py config
```

### Sync Commands

```bash
# Sync specific table
python migrate.py sync <table> [--record-id <id>]

# Examples:
python migrate.py sync users
python migrate.py sync memories --record-id "123e4567-e89b-12d3-a456-426614174000"
```

## API Endpoints

### Migration Management

- `GET /migration/status` - Get migration and sync status
- `POST /migration/migrate` - Start migration process
- `POST /migration/rollback` - Rollback migration
- `POST /migration/set-mode` - Set migration mode
- `POST /migration/validate` - Validate data integrity
- `POST /migration/backup` - Create SQLite backup

### Health and Monitoring

- `GET /migration/health` - Check database health
- `GET /migration/config` - Get migration configuration
- `POST /migration/sync` - Manual data synchronization

## Data Validation

The system performs comprehensive data validation:

### 1. Record Count Validation
- Compares record counts between SQLite and PostgreSQL
- Ensures no data loss during migration

### 2. Foreign Key Validation
- Validates referential integrity
- Checks for orphaned records

### 3. UUID Validation
- Validates UUID format and uniqueness
- Ensures proper UUID preservation

### 4. Data Type Validation
- Validates data type conversion accuracy
- Handles JSON, datetime, and enum conversions

## Error Handling and Recovery

### Automatic Recovery
- Connection health monitoring
- Automatic retry for failed operations
- Graceful degradation in dual-write mode

### Manual Recovery
```bash
# Check sync status
python migrate.py status

# Sync specific failed records
python migrate.py sync memories --record-id <failed-id>

# Full table resync
python migrate.py sync memories

# Rollback if needed
python migrate.py rollback --confirm
```

## Performance Considerations

### Batch Processing
- Configurable batch sizes (default: 100 records)
- Memory-efficient processing for large datasets
- Progress tracking and resumable operations

### Connection Pooling
- Efficient database connection management
- Connection health monitoring
- Automatic reconnection on failures

### Indexing
- Preserves all existing indexes
- Maintains query performance during transition

## Security Considerations

### Data Protection
- Automatic SQLite backup before migration
- Rollback capabilities for data safety
- Validation checks prevent data corruption

### Access Control
- Migration endpoints require authentication
- Role-based access to migration functions
- Audit logging for all migration operations

## Monitoring and Logging

### Migration Logs
```bash
# View migration logs
tail -f logs/migration.log

# Check error logs
grep ERROR logs/migration.log
```

### Health Monitoring
```bash
# Continuous health monitoring
watch -n 30 'python migrate.py health'

# Status dashboard
python migrate.py status
```

## Troubleshooting

### Common Issues

1. **Connection Failures**
   ```bash
   # Check database connectivity
   python migrate.py health
   
   # Verify environment variables
   python migrate.py config
   ```

2. **Data Validation Errors**
   ```bash
   # Run detailed validation
   python migrate.py validate
   
   # Check specific table
   python migrate.py sync <table>
   ```

3. **Performance Issues**
   ```bash
   # Reduce batch size
   export MIGRATION_BATCH_SIZE=50
   
   # Monitor progress
   python migrate.py status
   ```

### Recovery Procedures

1. **Migration Failure**
   ```bash
   # Check status
   python migrate.py status
   
   # Rollback if needed
   python migrate.py rollback --confirm
   
   # Restore from backup
   cp api/openmemory_backup_*.db api/openmemory.db
   ```

2. **Sync Issues**
   ```bash
   # Manual sync
   python migrate.py sync <table>
   
   # Validate after sync
   python migrate.py validate
   ```

## Testing

### Pre-Migration Testing
```bash
# Test with dry run
python migrate.py migrate --dry-run

# Validate current state
python migrate.py validate

# Check health
python migrate.py health
```

### Post-Migration Testing
```bash
# Validate migrated data
python migrate.py validate

# Test API endpoints
curl -X GET http://localhost:8000/migration/status

# Verify application functionality
python -m pytest tests/
```

## Migration Checklist

### Pre-Migration
- [ ] Backup SQLite database
- [ ] Configure PostgreSQL connection
- [ ] Verify database health
- [ ] Set migration mode to `dual_write`
- [ ] Test with dry run

### During Migration
- [ ] Monitor migration progress
- [ ] Check for errors in logs
- [ ] Validate data integrity
- [ ] Test application functionality

### Post-Migration
- [ ] Complete data validation
- [ ] Switch to `supabase_only` mode
- [ ] Final application testing
- [ ] Archive SQLite backup
- [ ] Update documentation

## Support and Maintenance

### Regular Maintenance
- Monitor database health
- Review migration logs
- Update batch sizes as needed
- Validate data integrity periodically

### Backup Strategy
- Automated SQLite backups before migration
- Regular PostgreSQL backups via Supabase
- Point-in-time recovery capabilities

## Conclusion

This migration system provides a robust, zero-downtime path from SQLite to Supabase PostgreSQL while maintaining full system functionality. The dual-write approach ensures data safety and allows for gradual transition with comprehensive validation and monitoring capabilities.

For additional support or questions, refer to the API documentation or contact the development team.