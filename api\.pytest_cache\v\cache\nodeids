["tests/test_evolution_comprehensive.py::TestEvolutionPrompts::test_default_technical_prompts_exist", "tests/test_evolution_comprehensive.py::TestEvolutionPrompts::test_fact_extraction_prompt_structure", "tests/test_evolution_comprehensive.py::TestEvolutionPrompts::test_update_memory_prompt_structure", "tests/test_evolution_comprehensive.py::TestEvolutionService::test_calculate_daily_metrics", "tests/test_evolution_comprehensive.py::TestEvolutionService::test_extract_evolution_stats_fallback", "tests/test_evolution_comprehensive.py::TestEvolutionService::test_extract_evolution_stats_with_results", "tests/test_evolution_comprehensive.py::TestEvolutionService::test_get_evolution_metrics_formatting", "tests/test_evolution_comprehensive.py::TestEvolutionService::test_get_evolution_monitor_formatting", "tests/test_evolution_comprehensive.py::TestEvolutionService::test_get_learning_insights_no_data", "tests/test_evolution_comprehensive.py::TestEvolutionService::test_store_evolution_operation", "tests/test_evolution_comprehensive.py::TestMCPToolsIntegration::test_evolution_metrics_mcp_tool_call", "tests/test_evolution_comprehensive.py::TestMCPToolsIntegration::test_mcp_tools_import_evolution_service", "tests/test_evolution_comprehensive.py::TestMemoryServiceIntegration::test_evolution_tracking_in_add_memory", "tests/test_evolution_comprehensive.py::TestMemoryServiceIntegration::test_track_evolution_operations_error_handling"]